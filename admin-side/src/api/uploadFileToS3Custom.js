import {PutObjectCommand, S3Client} from '@aws-sdk/client-s3'
import Methods from './methods'

export default {
  /*
   * Custom upload with tenant_no and exhibition_no in prefix
   * Type: upload type. Ex: 'item-ancillary'
   * File: file object
   * TenantNo: tenant number for prefix
   * ExhibitionNo: exhibition number for prefix
   */
  upload(api_type, file, tenantNo, exhibitionNo, callback) {
    Promise.resolve()
      .then(() => {
        // Read content from the file
        return this.readFileAllBrowsers(file)
      })
      .then(content => {
        if (typeof content === 'undefined' || content === null) {
          const err = {
            status: 404,
            message: 'Read file failed!',
          }
          return Promise.reject(err)
        }
        return content
      })
      .then(fileContent => {
        return this.bufferToS3Custom(api_type, file.name, file.type, fileContent, tenantNo, exhibitionNo)
      })
      .then(ret => {
        return callback(ret)
      })
      .catch(error => {
        return callback(error)
      })
  },

  bufferToS3Custom(api_type, fileName, fileType, fileContent, tenantNo, exhibitionNo) {
    return Promise.resolve()
      .then(() => {
        // Setting up S3 upload parameters
        return Methods.apiExecute('get-aws-credentials', {
          type: api_type,
        })
      })
      .then(response => {
        console.log('response = ', response)
        if (response.status === 200) {
          // Get reference to S3 client
          const s3Info = response.data
          const client = new S3Client({
            region: s3Info.region,
            credentials: {
              accessKeyId: s3Info.credentials.accessKeyId,
              secretAccessKey: s3Info.credentials.secretAccessKey,
              sessionToken: s3Info.credentials.sessionToken,
            },
          })

          // Create custom prefix with tenant_no and exhibition_no
          const currentDate = new Date()
          const dateString = this.dateToString(currentDate)
          const randomString = this.randomString(10)

          let customPrefix = `${s3Info.prefix_key}`

          // Add tenant_no and exhibition_no to prefix if provided
          if (tenantNo) {
            customPrefix += `-tenant${tenantNo}`
          }
          if (exhibitionNo) {
            customPrefix += `-exhibition${exhibitionNo}`
          }

          const key = `${customPrefix}/${fileName}`

          console.log('🎯 Custom upload key:', key)
          console.log('📝 Original prefix:', s3Info.prefix_key)
          console.log('🏢 Tenant No:', tenantNo)
          console.log('🎪 Exhibition No:', exhibitionNo)

          const uploadParams = {
            Bucket: s3Info.bucket,
            Key: key,
            Body: fileContent,
            ContentType: fileType,
          }
          return client.send(new PutObjectCommand(uploadParams)).then(() => {
            return Promise.resolve({
              status: 200,
              message: key,
            })
          })
        }
        const err = {
          status: response.status,
          message: response.message,
        }
        return Promise.reject(err)
      })
  },

  // Helper functions
  dateToString(date) {
    if (!date) {
      return ''
    }
    return [
      date.getFullYear(),
      `0${date.getMonth() + 1}`.slice(-2),
      `0${date.getDate()}`.slice(-2),
      `0${date.getHours()}`.slice(-2),
      `0${date.getMinutes()}`.slice(-2),
      `0${date.getSeconds()}`.slice(-2),
    ].join('')
  },

  randomString(length) {
    let result = ''
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    const charactersLength = characters.length
    for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * charactersLength))
    }
    return result
  },

  readFileAllBrowsers(file) {
    return new Promise((resolve, reject) => {
      if (file) {
        const reader = new FileReader()
        reader.readAsArrayBuffer(file)
        reader.onload = evt => {
          resolve(evt.target.result)
        }
        reader.onerror = evt => {
          reject(evt)
        }
      } else {
        reject({})
      }
    })
  },
}
