<script setup>
  import {getImageUrl} from '@/composables/common'
  import {PATH_NAME} from '@/defined/const'
  import {onMounted, ref} from 'vue'
  import {RouterLink} from 'vue-router'

  // Sample S3 file paths (without CloudFront URL) - để test common method
  const sampleImagePaths = ref([
    {
      path: 'item-ancillary/20250805170037-XCuwqCdFYh/scooter-xanh-la-cay.png',
      alt: 'Scooter xanh lá cây (Cấu trúc cũ)',
      title: 'Scooter xanh lá cây - Cấu trúc cũ từ S3',
    },
    {
      // Ảnh với tenant_no và exhibition_no mới
      path: 'item-ancillary/20250829172952-JTDYhrl6Hm-tenant1-exhibition37/children-bicycle-cute.png',
      alt: 'Sample Product với Tenant và Exhibition Info',
      title: 'Sample Product - Cấu trúc mới với Tenant & Exhibition',
    },
    {
      // Thêm một số ảnh khác để test cấu trúc mới
      path: 'item-ancillary/20250829120100-KlMnOpQrSt-tenant2-exhibition456/bicycle-red.png',
      alt: 'Bicycle Red với cấu trúc mới',
      title: 'Bicycle Red - Tenant 2, Exhibition 456',
    },
    {
      // Ảnh chỉ có tenant_no
      path: 'item-ancillary/20250829120200-UvWxYzAbCd-tenant3/motorbike-blue.jpg',
      alt: 'Motorbike Blue chỉ có tenant',
      title: 'Motorbike Blue - Chỉ có Tenant 3',
    },
  ])

  // Convert paths to full URLs using common method
  const sampleImages = ref(
    sampleImagePaths.value.map(item => ({
      ...item,
      src: getImageUrl(item.path), // Sử dụng common method
    }))
  )

  const imageLoadErrors = ref(new Set())

  const handleImageError = index => {
    imageLoadErrors.value.add(index)
    console.warn(`Image ${index} failed to load:`, sampleImages.value[index].src)
  }

  const handleImageLoad = index => {
    console.log(`Image ${index} loaded successfully:`, sampleImages.value[index].src)
  }

  onMounted(() => {
    console.log('📸 Using common method getImageUrl() for image URLs')
    console.log(
      '✅ Sample image URLs generated:',
      sampleImages.value.map(img => img.src)
    )

    // Demo với array của images (như type trong requirement)
    const mockImages = [
      {
        file_path: 'item-ancillary/20250805170037-XCuwqCdFYh/scooter-xanh-la-cay.png',
        postar_file_path: '',
      },
      {
        file_path:
          'item-ancillary/20250829172952-JTDYhrl6Hm-tenant1-exhibition37/children-bicycle-cute.png',
        postar_file_path: '',
      },
    ]
    console.log(
      '🔧 Demo with images array:',
      mockImages.map(img => getImageUrl(img.file_path))
    )
  })

  // import {computed, onBeforeMount, reactive, watch} from 'vue'
  // import {useRoute} from 'vue-router'
  // import {useLocale} from 'vuetify'
  // import useNotice from '../composables/useNotice'
  // import {PATH_NAME} from '../defined/const'

  // const route = useRoute()
  // const {getNotices, notices} = useNotice()
  // const notice = reactive({notice_no: null, title: '', sub_title: '', body: '', create_date: null})
  // const {t: translate, current} = useLocale()

  // const getData = async () => {
  //   await getNotices({
  //     limit: null,
  //   })
  //   const found = notices.find(item => item.notice_no === +route.params.noticeNo)
  //   if (found) {
  //     Object.assign(notice, found)
  //   }
  // }

  // onBeforeMount(async () => {
  //   await getData()
  // })

  // // 言語変更時にデータを再取得する
  // watch(
  //   () => current.value,
  //   () => {
  //     getData()
  //   }
  // )

  // // 緊急お知らせ抽出
  // const EmergencyNotificationList = computed(() => {
  //   return notices.filter(x => x.display_code === 3)
  // })

  // // Notice.notice_no が EmergencyNotificationList.notice_no と等しいことを確認する。
  // const isEmergencyNotification = computed(() => {
  //   return EmergencyNotificationList.value.some(x => x.notice_no === notice.notice_no)
  // })

  // const getFile = key => {
  //   if (key) {
  //     window.open(
  //       import.meta.env.VITE_API_ENDPOINT.replace('api/', '') + encodeURIComponent(key),
  //       '_blank'
  //     )
  //   }
  // }
</script>

<template>
  <main id="main">
    <div id="pNav">
      <ul>
        <li><a href="./">TOP</a></li>
        <li><a href="./news/newslist.html">お知らせ</a></li>
        <li>【オークション開催】2027年10月25日（木）10:00より、オークション開催のお知らせ</li>
      </ul>
    </div>

    <section class="news">
      <div class="container">
        <div class="news-detail-contents">
          <div class="news-head">
            <h2>
              <span class="date">2024.09.25</span>
              <span class="title"
                >【オークション開催】2027年10月25日（木）10:00より、オークション開催のお知らせ</span
              >
            </h2>
          </div>
          <div class="news-article">
            <!-- S3 Images Demo Section -->
            <div
              class="s3-images-demo"
              style="
                margin-bottom: 30px;
                padding: 20px;
                border: 2px dashed #ccc;
                border-radius: 8px;
              "
            >
              <h3 style="color: #333; margin-bottom: 20px">
                📸 Demo: Images từ S3 Bucket qua CloudFront
              </h3>
              <div
                style="
                  display: grid;
                  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                  gap: 20px;
                "
              >
                <div
                  v-for="(image, index) in sampleImages"
                  :key="index"
                  style="
                    border: 1px solid #ddd;
                    border-radius: 8px;
                    padding: 15px;
                    background-color: #f9f9f9;
                  "
                >
                  <h4 style="margin: 0 0 10px 0; color: #555">{{ image.title }}</h4>
                  <div
                    style="
                      position: relative;
                      width: 100%;
                      height: 200px;
                      background-color: #f0f0f0;
                      border-radius: 4px;
                      overflow: hidden;
                    "
                  >
                    <img
                      :src="image.src"
                      :alt="image.alt"
                      @load="handleImageLoad(index)"
                      @error="handleImageError(index)"
                      style="width: 100%; height: 100%; object-fit: cover"
                      v-if="!imageLoadErrors.has(index)"
                    />
                    <div
                      v-if="imageLoadErrors.has(index)"
                      style="
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        height: 100%;
                        color: #999;
                        text-align: center;
                        padding: 20px;
                      "
                    >
                      <div>
                        <div style="font-size: 24px; margin-bottom: 10px">🚫</div>
                        <div style="font-size: 14px">Image không load được</div>
                        <div style="font-size: 12px; margin-top: 5px; word-break: break-all">
                          {{ image.src }}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    style="margin-top: 10px; font-size: 12px; color: #666; word-break: break-all"
                  >
                    <strong>URL:</strong> {{ image.src }}
                  </div>
                  <div style="margin-top: 5px; font-size: 12px">
                    <span
                      :style="{
                        color: imageLoadErrors.has(index) ? '#e74c3c' : '#27ae60',
                        fontWeight: 'bold',
                      }"
                    >
                      Status: {{ imageLoadErrors.has(index) ? '❌ Failed' : '✅ Loaded' }}
                    </span>
                  </div>
                </div>
              </div>
              <div
                style="
                  margin-top: 20px;
                  padding: 15px;
                  background-color: #e8f4fd;
                  border-radius: 4px;
                  font-size: 14px;
                  line-height: 1.5;
                "
              >
                <strong>💡 Giải thích cấu trúc file name mới:</strong><br />
                • <strong>Cấu trúc cũ:</strong>
                <code>{type}/{dateString}-{randomString}/{fileName}</code><br />
                &nbsp;&nbsp;Ví dụ: <code>item-ancillary/20250805170037-XCuwqCdFYh/scooter.png</code
                ><br /><br />
                • <strong>Cấu trúc mới:</strong>
                <code
                  >{type}/{dateString}-{randomString}-tenant{tenantNo}-exhibition{exhibitionNo}/{fileName}</code
                ><br />
                &nbsp;&nbsp;Ví dụ:
                <code
                  >item-ancillary/20250829120000-AbCdEfGhIj-tenant1-exhibition123/product.jpg</code
                ><br /><br />
                • S3 Policy chỉ cho phép CloudFront OAI access, không có public access<br />
                • Images phải được truy cập qua CloudFront URL: <code>{{ baseCloudFrontUrl }}</code
                ><br />
                • Nếu image không load được, có thể file không tồn tại hoặc path không đúng<br />
                • Check Developer Tools Console để xem chi tiết lỗi<br /><br />
                <strong>🔧 Các thông tin được thêm vào prefix:</strong><br />
                • <code>tenant_no</code>: ID của tenant (khách hàng)<br />
                • <code>exhibition_no</code>: ID của exhibition (phiên đấu giá)<br />
                • Giúp phân loại và quản lý file theo tenant và exhibition
              </div>
            </div>

            <div class="article-contents">
              <p>
                このたび、キャンペーンに伴い特別オークションを開催します。<br />

                今回はアップサイクルトートバック＆ショルダーバック＆サコッシュを販売します。<br />

                皆さまのご参加をお待ちしています。<br />
                <br />
                ■オークション開催期間<br />

                2025年8月22日（金）18:00 ～ 8月24日（日）22:00<br />
                <br />
                ■出品商品<br />
                ・トートバッグ
                <br />
                ・ショルダーバッグ
                <br />
                ・サコッシュ
                <br />
                ※出品内容（サイズ等）は下記オークションサイトにてご確認ください。<br />
                <br />
                ■オークションページ<br />
                https://cloudec-auction/auction/20250412
                <br />
                <br />
                ■オークション参加方法<br />
                ①「新規会員登録」より会員登録<br />
                ②各ページの入札ボタンからご入札<br />
                <br />
                ■落札後のフローについて<br />
                オークション終了後に、運営事務局からご登録のメールアドレス宛に『落札通知連絡』が届きます。<br />
                支払い等のお手続きはそちらのメール内容に従って行ってください。
                <br />
                ※万が一メールが見当たらない場合は、キャリア様の設定により、迷惑メールフィルタがかかっている場合があります。
                <br /><br />

                ■注意事項<br />
                ・オークションサイト金額表示は『税抜表示』です。<br />
                ・性質上、傷や汚れがある場合がございますので、入札の際にはあらかじめご了承ください。<br />
                ・事前予告なく、開催の有無、内容の変更等を行う場合があります。<br />
                ・お支払い方法等オークションのシステムについてはご利用ガイドを必ずご確認ください。<br />
                ※FAQはこちら<br />
                <br />
                ■お問合せ先<br />
                本オークションに関するお問い合わせは、ショップへお問い合わせ下さい。<br />
                運営事務局（00-1234-1234）<br />
                <EMAIL><br />
                <br />
                ■オークション運営<br />
                GMOメイクショップ株式会社<br />
                <br />
              </p>
            </div>
          </div>
          <div class="back-list">
            <RouterLink :to="PATH_NAME.NOTICE_LIST" class="btn back">
              <span>お知らせ一覧へ戻る</span>
            </RouterLink>
          </div>
        </div>
      </div>
    </section>
  </main>
</template>
