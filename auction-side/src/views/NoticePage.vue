<script setup>
  import {PATH_NAME} from '@/defined/const'
  import {RouterLink} from 'vue-router'

  // import {computed, onBeforeMount, reactive, watch} from 'vue'
  // import {useRoute} from 'vue-router'
  // import {useLocale} from 'vuetify'
  // import useNotice from '../composables/useNotice'
  // import {PATH_NAME} from '../defined/const'

  // const route = useRoute()
  // const {getNotices, notices} = useNotice()
  // const notice = reactive({notice_no: null, title: '', sub_title: '', body: '', create_date: null})
  // const {t: translate, current} = useLocale()

  // const getData = async () => {
  //   await getNotices({
  //     limit: null,
  //   })
  //   const found = notices.find(item => item.notice_no === +route.params.noticeNo)
  //   if (found) {
  //     Object.assign(notice, found)
  //   }
  // }

  // onBeforeMount(async () => {
  //   await getData()
  // })

  // // 言語変更時にデータを再取得する
  // watch(
  //   () => current.value,
  //   () => {
  //     getData()
  //   }
  // )

  // // 緊急お知らせ抽出
  // const EmergencyNotificationList = computed(() => {
  //   return notices.filter(x => x.display_code === 3)
  // })

  // // Notice.notice_no が EmergencyNotificationList.notice_no と等しいことを確認する。
  // const isEmergencyNotification = computed(() => {
  //   return EmergencyNotificationList.value.some(x => x.notice_no === notice.notice_no)
  // })

  // const getFile = key => {
  //   if (key) {
  //     window.open(
  //       import.meta.env.VITE_API_ENDPOINT.replace('api/', '') + encodeURIComponent(key),
  //       '_blank'
  //     )
  //   }
  // }
</script>

<template>
  <main id="main">
    <div id="pNav">
      <ul>
        <li><a href="./">TOP</a></li>
        <li><a href="./news/newslist.html">お知らせ</a></li>
        <li>【オークション開催】2027年10月25日（木）10:00より、オークション開催のお知らせ</li>
      </ul>
    </div>

    <section class="news">
      <div class="container">
        <div class="news-detail-contents">
          <div class="news-head">
            <h2>
              <span class="date">2024.09.25</span>
              <span class="title"
                >【オークション開催】2027年10月25日（木）10:00より、オークション開催のお知らせ</span
              >
            </h2>
          </div>
          <div class="news-article">
            <div class="article-contents">
              <p>
                このたび、キャンペーンに伴い特別オークションを開催します。<br />

                今回はアップサイクルトートバック＆ショルダーバック＆サコッシュを販売します。<br />

                皆さまのご参加をお待ちしています。<br />
                <br />
                ■オークション開催期間<br />

                2025年8月22日（金）18:00 ～ 8月24日（日）22:00<br />
                <br />
                ■出品商品<br />
                ・トートバッグ
                <br />
                ・ショルダーバッグ
                <br />
                ・サコッシュ
                <br />
                ※出品内容（サイズ等）は下記オークションサイトにてご確認ください。<br />
                <br />
                ■オークションページ<br />
                https://cloudec-auction/auction/20250412
                <br />
                <br />
                ■オークション参加方法<br />
                ①「新規会員登録」より会員登録<br />
                ②各ページの入札ボタンからご入札<br />
                <br />
                ■落札後のフローについて<br />
                オークション終了後に、運営事務局からご登録のメールアドレス宛に『落札通知連絡』が届きます。<br />
                支払い等のお手続きはそちらのメール内容に従って行ってください。
                <br />
                ※万が一メールが見当たらない場合は、キャリア様の設定により、迷惑メールフィルタがかかっている場合があります。
                <br /><br />

                ■注意事項<br />
                ・オークションサイト金額表示は『税抜表示』です。<br />
                ・性質上、傷や汚れがある場合がございますので、入札の際にはあらかじめご了承ください。<br />
                ・事前予告なく、開催の有無、内容の変更等を行う場合があります。<br />
                ・お支払い方法等オークションのシステムについてはご利用ガイドを必ずご確認ください。<br />
                ※FAQはこちら<br />
                <br />
                ■お問合せ先<br />
                本オークションに関するお問い合わせは、ショップへお問い合わせ下さい。<br />
                運営事務局（00-1234-1234）<br />
                <EMAIL><br />
                <br />
                ■オークション運営<br />
                GMOメイクショップ株式会社<br />
                <br />
              </p>
            </div>
          </div>
          <div class="back-list">
            <RouterLink :to="PATH_NAME.NOTICE_LIST" class="btn back">
              <span>お知らせ一覧へ戻る</span>
            </RouterLink>
          </div>
        </div>
      </div>
    </section>
  </main>
</template>
