// Import { useRouter } from 'vue-router'
import {PutObjectCommand, S3Client} from '@aws-sdk/client-s3'
import CryptoJS from 'crypto-js'
import {format as formatFns} from 'date-fns'
import {toZonedTime} from 'date-fns-tz'
import {computed} from 'vue'
import {PATH_NAME} from '../defined/const'
import router from '../router/index'
import {useLanguageStore} from '../stores/language'

const cryptoJsKey = import.meta.env.VITE_CRYPTOJS_KEY
const cryptoJsIv = import.meta.env.VITE_CRYPTOJS_IV

export const isRegist = computed(
  () => PATH_NAME.ENTRY_INFO_REGIST === router.currentRoute.value.path
)
export const isConfirm = computed(
  () => PATH_NAME.ENTRY_INFO_CONFIRM === router.currentRoute.value.path
)

export const scrollToTop = (id = 'entry-form') => {
  document.getElementById(id)?.scrollIntoView({behavior: 'smooth'})
  /*
   * Window.scrollTo({
   *   top: 0,
   *   left: 0,
   *   behavior: 'smooth'
   * })
   */
}

export const convertFullWidthToHalfWidth = input => {
  return input.toString().replace(/[Ａ-Ｚａ-ｚ０-９！-～ー]/g, char => {
    const fullWidth =
      'ＡＢＣＤＥＦＧＨＩＪＫＬＭＮＯＰＱＲＳＴＵＶＷＸＹＺａｂｃｄｅｆｇｈｉｊｋｌｍｎｏｐｑｒｓｔｕｖｗｘｙｚ０１２３４５６７８９！＂＃＄％＆＇（）＊＋，－．／：；＜＝＞？＠［＼］＾＿｀｛｜｝～ー'
    const halfWidth =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!"#$%&\'()*+,-./:;<=>?@[\\]^_`{|}~-'
    const index = fullWidth.indexOf(char)
    return index === -1 ? char : halfWidth.charAt(index)
  })
}

export const aesEncrypt = object => {
  const cipher = CryptoJS.AES.encrypt(
    JSON.stringify(object),
    CryptoJS.enc.Utf8.parse(cryptoJsKey),
    {
      iv: CryptoJS.enc.Utf8.parse(cryptoJsIv),
      mode: CryptoJS.mode.CBC,
    }
  )

  return cipher.toString()
}

export const aesDecrypt = txt => {
  const cipher = CryptoJS.AES.decrypt(txt, CryptoJS.enc.Utf8.parse(cryptoJsKey), {
    iv: CryptoJS.enc.Utf8.parse(cryptoJsIv),
    mode: CryptoJS.mode.CBC,
  })

  return JSON.parse(CryptoJS.enc.Utf8.stringify(cipher).toString())
}

window.mobileCheck = () => {
  let check = false
  const agent = navigator.userAgent || navigator.vendor || window.opera
  if (
    /(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i.test(
      agent
    ) ||
    /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(
      agent.substr(0, 4)
    )
  ) {
    check = true
  }
  return check
}

export const isPC = !window.mobileCheck()

export const checkInput = (input, length = 200, type = 'string', required = true) => {
  if (required && (!input || input.length === 0)) {
    return {
      valid: false,
      errorMsg: '入力が必要です。',
    }
  }
  if (input.length > length) {
    return {
      valid: false,
      errorMsg: `${length}文字以内に入力してください。`,
    }
  }
  if (type === 'antique' && !/^(?:\d{0}|(?:\d{12}))$/.test(input)) {
    return {
      valid: false,
      errorMsg: '古物商許可番号は12桁数字を入力してください。',
    }
  }
  if (type === 'postcode' && !/^\d{3}-?\d{4}$/.test(input)) {
    return {
      valid: false,
      errorMsg: '郵便番号が間違ってます。',
    }
  }
  if (type === 'tel' && !/^\+?\d{1,19}$/.test(input)) {
    return {
      valid: false,
      errorMsg: '電話番号が間違ってます。',
    }
  }
  if (
    type === 'password' &&
    !/^(?=.*?[a-zA-Z])(?=.*?[0-9])[a-zA-Z0-9$@$!%*#?&_-]{8,14}$/.test(input)
  ) {
    return {
      valid: false,
      errorMsg: 'パスワードは8桁以上14桁以内に入力してください。',
    }
  }
  if (
    type === 'email' &&
    !/^[a-zA-Z0-9_.+-]+@([a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]*\.)+[a-zA-Z]{2,}$/.test(input)
  ) {
    return {
      valid: false,
      errorMsg: 'メールの値が間違ってます。',
    }
  }
  return {
    valid: true,
  }
}

// Show date-time as local (eg. Japan time, Dubai time)
export const formatDateStringLocale = ({dateString, timeZone, dateFormat}) => {
  // Check if dateString in valid
  const dateObj = new Date(dateString)
  if (isNaN(dateObj.getTime())) {
    return {datePart: 'Invalid Date', timePart: 'Invalid Time'}
  }
  const zonedDateTime = toZonedTime(dateObj, timeZone)
  const datePart = formatFns(zonedDateTime, dateFormat)
  const timePart = formatFns(zonedDateTime, 'HH:mm')
  return {datePart, timePart}
}

// Output:
//  → 8月31日 23時50分 (Japanese)
//  → 8/31 23:50 (English).
export const formatDateString = dateString => {
  const langStore = useLanguageStore()

  const dateObj = new Date(dateString)
  if (isNaN(dateObj.getTime())) {
    return {datePart: 'Invalid Date', timePart: 'Invalid Time'}
  }

  // Choose date format
  const dateFormat = langStore.language === 'ja' ? 'M月d日' : 'M/d' // 👈 remove leading zero

  const datePart = formatFns(dateObj, dateFormat)

  // Format for the time (HH時MM分 in Japanese, HH:MM otherwise)
  const timePart =
    langStore.language === 'ja'
      ? formatFns(dateObj, 'H時mm分') //  Japanese style
      : formatFns(dateObj, 'HH:mm') // English style

  return {datePart, timePart}
}

export const fullDateFormat = dateString => {
  const date = new Date(dateString)
  const daysOfWeekJapanese = ['日', '月', '火', '水', '木', '金', '土']

  /*
   * Const year = date.getFullYear()
   * const month = (date.getMonth() + 1).toString().padStart(2, '0') // Adding 1 since getMonth() returns zero-based month index
   * const day = date.getDate().toString().padStart(2, '0')
   */
  const hours = date.getHours().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')

  const formattedDate = `${date.toLocaleString('ja-jp', {
    dateStyle: 'short',
  })} (${daysOfWeekJapanese[date.getDay()]}) ${hours}:${minutes}`
  // Console.log(formattedDate); // Output: "2023/08/04 (Aug) 17:29"
  return formattedDate
}

export const addExternalScripts = (scripts, async = false) => {
  for (script of scripts) {
    const tag = document.createElement('script')
    if (async) {
      tag.setAttribute('async', 'async')
    }
    tag.setAttribute('src', `/js/${script}`)
    document.head.appendChild(tag)
  }
}

export const getUploadCredentials = type => {
  const params = {
    type,
  }
  return apiExecute('public/get-aws-credentials', params, false, 'ja')
}

export const readFileAllBrowsers = file => {
  return new Promise((resolve, reject) => {
    if (file) {
      const reader = new FileReader()
      reader.readAsArrayBuffer(file)
      reader.onload = evt => {
        resolve(evt.target.result)
      }
      reader.onerror = evt => {
        reject(evt)
      }
    } else {
      reject({})
    }
  })
}

export const uploadToS3 = (s3Info, file) => {
  return Promise.resolve()
    .then(() => readFileAllBrowsers(file))
    .then(fileContent => {
      const client = new S3Client({
        region: s3Info.region,
        credentials: {
          accessKeyId: s3Info.credentials.accessKeyId,
          secretAccessKey: s3Info.credentials.secretAccessKey,
          sessionToken: s3Info.credentials.sessionToken,
        },
      })

      const key = `${s3Info.prefix_key}/${file.name}`

      const uploadParams = {
        Bucket: s3Info.bucket,
        Key: key,
        Body: fileContent,
        ContentType: file.type,
      }
      return client.send(new PutObjectCommand(uploadParams)).then(() => {
        return key
      })
    })
}

/**
 * Formats a numeric value into a localized string representation with optional length restriction.
 * This function handles already-formatted locale strings and prevents NaN errors on repeated calls.
 * It supports all international locale formats including French (spaces), German (periods), Arabic, etc.
 *
 * @param {string|number} value - The input value to format as a localized string. Can be a number, numeric string, or already-formatted locale string.
 * @param {number} [maxLength] - Optional. If provided, limits the maximum length of the numeric value.
 * @returns {string} The formatted localized string representation of the number.
 * @example
 * priceLocaleString(1234567)      // "1,234,567" (en-US) or "1 234 567" (fr-FR)
 * priceLocaleString("1 234")      // "1,234" (en-US) or "1 234" (fr-FR)
 * priceLocaleString("1.234")      // "1,234" (en-US) or "1 234" (fr-FR)
 * priceLocaleString(1234567, 4)   // "1,234" (en-US) or "1 234" (fr-FR)
 * priceLocaleString("abc,123")    // ""
 */
export const priceLocaleString = (value, maxLength) => {
  if (value === null || value === undefined || value === '') {
    return ''
  }

  const stringValue = String(value)

  // Extract only numeric digits from the string, removing all locale-specific separators
  // This regex covers all common locale separators:
  // - Comma (,) - used in en-US, ja-JP, etc.
  // - Period (.) - used as thousands separator in de-DE, es-ES, etc.
  // - Regular space ( ) - used in fr-FR, ru-RU, etc.
  // - Non-breaking space (\u00A0) - used in some European locales
  // - Thin space (\u2009) - used in some locales
  // - Hair space (\u200A) - used in some locales
  // - Narrow no-break space (\u202F) - used in fr-FR and other locales
  // - Medium mathematical space (\u205F) - used in some mathematical contexts
  // - Ideographic space (\u3000) - used in some Asian locales
  const numericOnly = stringValue.replace(/[,.\s\u00A0\u2009\u200A\u202F\u205F\u3000]/g, '')

  // Filter out any remaining non-numeric characters (like letters)
  const digits = numericOnly
    .split('')
    .filter(char => !isNaN(char) && char !== '')
    .join('')

  // Return empty string if no valid digits found
  if (digits === '') {
    return ''
  }

  // Apply maxLength restriction if specified
  const finalDigits = maxLength ? digits.substring(0, maxLength) : digits

  // Convert to number and format according to user's locale
  const number = Number(finalDigits)

  // Handle edge case where Number conversion fails
  if (isNaN(number)) {
    return ''
  }

  return number.toLocaleString()
}

/**
 * Converts a localized string representation of a number into a numeric value.
 * This function handles all international locale formats and separators.
 *
 * @param {string|number} value - The input value to convert to a number. Can be a localized string or a numeric value.
 * @returns {number} The numeric value converted from the localized string.
 * @example
 * localeString2Number("1,234,567")  // 1234567 (en-US format)
 * localeString2Number("1 234 567")  // 1234567 (fr-FR format)
 * localeString2Number("1.234.567")  // 1234567 (de-DE format)
 * localeString2Number("abc,123")    // 123
 * localeString2Number("")           // 0
 */
export const localeString2Number = value => {
  // Handle null, undefined, or empty values
  if (value === null || value === undefined || value === '') {
    return 0
  }

  // Convert to string for processing
  const stringValue = String(value)

  // Remove all locale-specific separators (same comprehensive list as priceLocaleString)
  const numericOnly = stringValue.replace(/[,.\s\u00A0\u2009\u200A\u202F\u205F\u3000]/g, '')

  // Filter out any remaining non-numeric characters
  const digits = numericOnly
    .split('')
    .filter(char => !isNaN(char) && char !== '')
    .join('')

  // Return 0 if no valid digits found
  if (digits === '') {
    return 0
  }

  // Convert to number
  const number = Number(digits)

  // Handle edge case where Number conversion fails
  return isNaN(number) ? 0 : number
}

/*
 * Remaining time
 * const isTopScreen = true
 */
const daySplitter = '日'
const hourSplitter = '時間'
const minuteSplitter = '分'
const secondSplitter = '秒'
export const getRemainingTime = seconds => {
  let result = ''
  if (seconds > 0) {
    const day = Math.floor(seconds / 60 / 60 / 24)
    const hour = Math.floor((seconds / 60 / 60) % 24)
    const min = Math.floor((seconds / 60) % 60)
    const sec = Math.floor(seconds % 60)
    // Days
    if (day === 0) {
      result += ''
    } else if (day < 2) {
      result += day + daySplitter
    } else {
      result += day + daySplitter
    }
    // Hours
    if (hour === 0) {
      result += '' // Dont show 0 hour
    } else if (hour < 10) {
      result += `${hour}${hourSplitter}` // 01:22 -> 1時間22
    } else {
      result += hour + hourSplitter
    }
    // Minutes
    if (min < 10) {
      result += `${min}${minuteSplitter}` // 01:02 -> 1:2
    } else {
      result += min + minuteSplitter
    }
    /*
     * Seconds
     * if (sec < 10) {
     *   result += (isTopScreen ? '0' : '') + sec + secondSplitter // Dont add 0 when in detail screen: 0分2秒、TOP screen: 0:02
     * } else {
     *   result += sec + secondSplitter
     * }
     */
  }
  return result
}

export const format = (val, args) => {
  let a = val
  for (const k in args) {
    a = a.replace(`{${k}}`, args[k])
  }
  return a
}

export const pdfViewerUrl = pdfUrl => {
  return `/lib/pdfjs-2.10.377-dist/web/viewer.html?file=${pdfUrl}`
}

export const scrollToAnker = hash => {
  const target = $(hash)
  const position = target.offset()?.top
  if (!position) return
  $('body,html').stop().animate({scrollTop: position}, 500)
}

export const calculatePriceWithTax = (price, taxRate) => {
  if (price == null) return 0
  if (taxRate == null) return price
  return Math.floor(price * (1 + taxRate / 100))
}

// Get CloudFront base URL from environment
const getCloudFrontBaseUrl = () => {
  const apiEndpoint = import.meta.env.VITE_API_ENDPOINT
  if (!apiEndpoint) {
    console.warn('⚠️ VITE_API_ENDPOINT not found in environment variables')
    return ''
  }
  return apiEndpoint.replace('api/', '')
}

/**
 * Convert S3 file path to CloudFront URL for displaying images
 * @param {string} filePath - S3 file path (e.g., "item-ancillary/20250805170037-XCuwqCdFYh/scooter.png")
 * @param {string} [fallbackImage] - Optional fallback image path if filePath is invalid
 * @returns {string} CloudFront URL for the image
 *
 * @example
 * // Basic usage
 * getImageUrl("item-ancillary/20250805170037-XCuwqCdFYh/scooter.png")
 * // Returns: "https://d18yxra1tfyhwr.cloudfront.net/item-ancillary/20250805170037-XCuwqCdFYh/scooter.png"
 *
 * // With fallback
 * getImageUrl("", "/assets/img/no-image.png")
 * // Returns: "/assets/img/no-image.png"
 *
 * // Array of images
 * const images = [{file_path: "item-ancillary/path1/img1.jpg"}, {file_path: "item-ancillary/path2/img2.jpg"}]
 * const imageUrls = images.map(img => getImageUrl(img.file_path))
 */
export const getImageUrl = (filePath, fallbackImage = null) => {
  // Return fallback if filePath is invalid
  if (!filePath || typeof filePath !== 'string' || filePath.trim() === '') {
    return fallbackImage || ''
  }

  const baseUrl = getCloudFrontBaseUrl()
  if (!baseUrl) {
    console.warn('⚠️ CloudFront base URL not available, returning original filePath')
    return filePath
  }

  // Remove leading slash if present to avoid double slashes
  const cleanPath = filePath.startsWith('/') ? filePath.substring(1) : filePath

  const fullUrl = `${baseUrl}${cleanPath}`

  // Log for debugging (can be removed in production)
  console.debug('🖼️ Image URL generated:', {
    originalPath: filePath,
    generatedUrl: fullUrl,
    baseUrl: baseUrl,
  })

  return fullUrl
}

/**
 * Get the first image URL from an array of images
 * @param {Array} images - Array of image objects with file_path property
 * @param {string} [fallbackImage] - Optional fallback image path
 * @returns {string} CloudFront URL for the first image
 *
 * @example
 * const images = [
 *   {file_path: "item-ancillary/20250805170037-XCuwqCdFYh/scooter.png", postar_file_path: ""},
 *   {file_path: "item-ancillary/20250805170037-XCuwqCdFYh/bike.png", postar_file_path: ""}
 * ]
 * getFirstImageUrl(images, "/assets/img/no-image.png")
 */
export const getFirstImageUrl = (images, fallbackImage = null) => {
  if (!Array.isArray(images) || images.length === 0) {
    return fallbackImage || ''
  }

  const firstImage = images[0]
  if (!firstImage || !firstImage.file_path) {
    return fallbackImage || ''
  }

  return getImageUrl(firstImage.file_path, fallbackImage)
}

/**
 * Get all image URLs from an array of images
 * @param {Array} images - Array of image objects with file_path property
 * @returns {Array<string>} Array of CloudFront URLs
 *
 * @example
 * const images = [
 *   {file_path: "item-ancillary/path1/img1.jpg", postar_file_path: ""},
 *   {file_path: "item-ancillary/path2/img2.jpg", postar_file_path: ""}
 * ]
 * const imageUrls = getAllImageUrls(images)
 * // Returns: ["https://cloudfront.net/item-ancillary/path1/img1.jpg", "https://cloudfront.net/item-ancillary/path2/img2.jpg"]
 */
export const getAllImageUrls = images => {
  if (!Array.isArray(images)) {
    return []
  }

  return images
    .filter(image => image && image.file_path)
    .map(image => getImageUrl(image.file_path))
    .filter(url => url) // Remove empty URLs
}

/**
 * Get poster image URL (if available) or fallback to main image
 * @param {Object} image - Image object with file_path and postar_file_path properties
 * @param {string} [fallbackImage] - Optional fallback image path
 * @returns {string} CloudFront URL for poster or main image
 *
 * @example
 * const image = {file_path: "item-ancillary/path/img.jpg", postar_file_path: "item-ancillary/path/poster.jpg"}
 * getPosterImageUrl(image) // Returns poster URL
 *
 * const imageNoPoster = {file_path: "item-ancillary/path/img.jpg", postar_file_path: ""}
 * getPosterImageUrl(imageNoPoster) // Returns main image URL
 */
export const getPosterImageUrl = (image, fallbackImage = null) => {
  if (!image) {
    return fallbackImage || ''
  }

  // Try poster first, then main image, then fallback
  if (image.postar_file_path && image.postar_file_path.trim() !== '') {
    return getImageUrl(image.postar_file_path, fallbackImage)
  }

  if (image.file_path && image.file_path.trim() !== '') {
    return getImageUrl(image.file_path, fallbackImage)
  }

  return fallbackImage || ''
}

/**
 * Build gallery image objects (large and thumb) from images array structure
 * @param {Array<{file_path?: string|null, postar_file_path?: string|null}>} images
 * @param {Array<string>} [fallbackUrls] - Optional fallback URL list; if not provided, returns [] when images invalid
 * @returns {Array<{large: string, thumb: string, size: string}>}
 */
export const buildGalleryImagesFromArray = (images, fallbackUrls = null) => {
  if (!Array.isArray(images) || images.length === 0) {
    if (Array.isArray(fallbackUrls) && fallbackUrls.length > 0) {
      return fallbackUrls.map(url => ({large: url, thumb: url, size: '1200x1200'}))
    }
    return []
  }
  const urls = images
    .map(img => getPosterImageUrl(img))
    .filter(u => typeof u === 'string' && u.trim() !== '')

  if (urls.length === 0) {
    if (Array.isArray(fallbackUrls) && fallbackUrls.length > 0) {
      return fallbackUrls.map(url => ({large: url, thumb: url, size: '1200x1200'}))
    }
    return []
  }
  return urls.map(url => ({large: url, thumb: url, size: '1200x1200'}))
}
