export type BidStatus = {
  status: string
  can_bid: boolean
  sold_out: boolean
  started: boolean
  quantity: number
  tax_rate: number
  bid_price: number | null
  extending: boolean
  is_cancel: boolean
  top_price: number
  pitch_width: number
  bid_quantity: number | null
  end_datetime: string
  pitch_option: number
  current_price: number
  is_top_member: boolean | null
  is_more_little: boolean
  pitch_button_1: number
  pitch_button_2: number
  pitch_button_3: number
  start_datetime: string
  is_second_member: boolean | null
  lowest_bid_price: number
  automatic_bidding: boolean
  remaining_seconds: number
  lowest_bid_quantity: number
  top_member_nickname: string
  is_exceeding_lowest_price: boolean
  is_not_exceeding_lowest_price: boolean
  deal_bid_price?: number
}

export type AttentionInfo = {
  bid_count: number
  favorited_count?: number
  view_count?: number
  is_favorited?: boolean
  nyusatsu_seigen?: 'higher-than-current-price' | 'free' | 'higher-than-previous-own-bid'
  isCancelBidAllowed?: boolean
  isFastPriceAuction?: boolean
  bidCommitMinDisplayFlag?: number
  saiTeiRakuSatsuKakakuShiyouUmu?: boolean
}

export type FreeField = {
  product_name?: string
  description?: string
  category?: string
  condition?: string
  shipping_free?: boolean
  minimum_bid_price?: number
  maker?: string
  color?: string
  custom_fields?: Record<string, string>
}

// {
//     "model": "M51140",
//     "rankB": "通常程度の使用感・キズ・汚れが気にならない方におすすめ",
//     "rankC": "かなり使い込まれたキズ・汚れ・変色などが目立つ商品",
//     "newsprc": "NEWS価格",
//     "description": "細やかな装飾と精巧な作りが魅力的な、コレクターアイテムです。また、厳選された素材のみを使用し、熟練の技術者が手がけた傑作です。また、時代を超えて愛され続ける普遍的なデザインを採用しています。また、細やかな装飾と精巧な作りが魅力的な、コレクターアイテムです。また、エレガントな外観と機能美を追求した、プレミアムグレードの商品です。また、細やかな装飾と精巧な作りが魅力的な、コレクターアイテムです。また、上質な仕上がりと洗練されたスタイルが特徴的な製品となっております。また、優雅なデザインと実用性を兼ね備えた、まさに理想的なアイテムです。また、厳選された素材のみを使用し、熟練の技術者が手がけた傑作です。また、職人によって丁寧に作られた逸品で、細部にまでこだわりが感じられます。また、エレガントな外観と機能美を追求した、プレミアムグレードの商品です。また、上質な仕上がりと洗練されたスタイルが特徴的な製品となっております。また、エレガントな外観と機能美を追求した、プレミアムグレードの商品です。また、時代を超えて愛され続ける普遍的なデザインを採用しています。また、エレガントな外観と機能美を追求した、プレミアムグレードの商品です。また、伝統的な技法と現代的なセンスが融合した、特別な一品です。また、この商品は高品質な素材を使用しており、長年にわたってご愛用いただけます。また、上質な仕上がりと洗練されたスタイルが特徴的な製品となっております。また、伝統的な技法と現代的なセンスが融合した、特別な一品です。また、エレガントな外観と機能美を追求した、プレミアムグレードの商品です。また、この商品は高品質な素材を使用しており、長年にわたってご愛用いただけます。また、時代を超えて愛され続ける普遍的なデザインを採用しています。また、職人によって丁寧に作られた逸品で、細部にまでこだわりが感じられます。また、職人によって丁寧に作られた逸品で、細部にまでこだわりが感じられます。また、伝統的な技法と現代的なセンスが融合した、特別な一品です。また、厳選された素材のみを使用し、熟練の技術者が手がけた傑作です。また、時代を超えて愛され続ける普遍的なデザインを採用しています。また、上質な仕上がりと洗練されたスタイルが特徴的な製品となっております。また、職人によって丁寧に作られた逸品で、細部にまでこだわりが感じられます",
//     "shippingFee": "着払い（配送方法の詳細）",
//     "product_name": "見下期間グッチ ヴェルニ チェーンウォレット 正規品 希少品",
//     "shippingMethod": "宅急便（ヤマト運輸）",
//     "itemConditionNote": "店頭展示品、未使用品、目立った傷汚れなし"
// }

export type BidHistory = {
  bid_price: number
  bid_quantity: number
  bid_datetime: string
  create_datetime: string
  nickname?: string | null
  after_current_price: number
  user_id?: string
}

export type RawAuctionItem = {
  exhibition_item_no: string
  exhibition_no: string
  item_no: string
  category_id: number
  auction_classification?: number
  sold_out?: boolean
  free_field: FreeField
  bid_status: BidStatus
  attention_info: AttentionInfo
  start_datetime: string
  end_datetime: string
  bid_histories?: BidHistory[]
  status?: string
}

export type FormattedAuctionItem = RawAuctionItem & {
  link: string
  imgSrc: string
  currentPrice: string
  currentPriceTaxIncluded: string
  noOfBids: number
  endDatePart: string
  endTimePart: string
  startDatePart: string
  startTimePart: string
  bidPrice: string
  bidQuantity: string
  bidInputError: {
    bidPrice: string | null
    bidQuantity: string | null
  }
}

export type ExhibitionGroup = {
  exhibition_no: string
  exhibition_name: string
  start_datetime: string
  end_datetime: string
}

export type ProductList = {
  all: FormattedAuctionItem[]
  exhibitionList: ExhibitionGroup[]
}

export type AuctionDetails = {
  exhibition_item_no: string
  exhibition_no: string
  exhibition_name: string
  preview_end_datetime: string
  auction_classification?: number
  item_no: string
  view_count?: number
  freeFields?: FreeField
  mappingFreeFields?: Array<{label: string; value: string; display_area: string}>
  bid_histories?: BidHistory[]
  images: Array<{file_path: string; postar_file_path: string}>
  bid_status: BidStatus
  attention_info: AttentionInfo
  is_favorited: boolean
  category: string
  sold_out: boolean
  currentPrice: string
  currentPriceTaxIncluded: string
  endDatePart: string
  endTimePart: string
  startDatePart: string
  startTimePart: string
  favorite_count: number
  bid_count: number
  categoryCode: string
  itemNo: string
  postage?: string
  hasUserBid: boolean
}

export interface InquiryChat {
  exhibition_message_no: number
  update_category_id: string
  answer_exhibition_message_no: number
  message: string
  member_no: number | null
  member_name: string | null
  checked_admin_no: number | null
  checked_admin_name: string | null
  create_admin_no: number | null
  create_admin_name: string | null
  create_datetime: string
  answer_flag: number
  delete_flag: number
  no_answer_flag: number
  hidden_flag: number
}

export type ResponseInquiryChat = InquiryChat[]

// Extended AuctionDetails interface for reactive store usage
export interface ReactiveProductDetails extends Partial<AuctionDetails> {
  [key: string]: any
}

// Auction classification constants type
export type AuctionClassification = 'ascending' | 'sealed'
