<script setup lang="ts">
  import useBid from '@/composables/bid'
  import {formatDateString, getImageUrl, priceLocaleString} from '@/composables/common'
  import useFavorite from '@/composables/favorite'
  import useAuctionStatus from '@/composables/useAuctionStatus'
  import {usePrevRouteStore} from '@/stores/prev-route'
  import {computed, defineAsyncComponent, defineEmits, defineProps} from 'vue'

  import {useTypedI18n} from '@/language'
  import {useLanguageStore} from '@/stores/language'
  import {navigateToPath} from '@/utils'

  const ExtendCountDown = defineAsyncComponent(() => import('../../parts/ExtendCountDown.vue'))

  const props = defineProps(['item'])
  const emit = defineEmits(['refresh'])

  const {t: translate} = useTypedI18n()
  const {goToPath} = usePrevRouteStore()
  const {toggleFavorite} = useFavorite()
  const {bidPrice, bidQuantity, bidHandle, validateBidPrice, validateBidQuantity, bidCancelHandle} =
    useBid()
  const {getAuctionStatusClass} = useAuctionStatus()
  const languageStore = useLanguageStore()

  // Update value of bidPrice when mounted
  bidPrice.value = props.item?.bidPrice || ''

  const isEndAuction = computed(() => {
    if (!props.item?.bid_status?.end_datetime) return false
    const now = new Date()
    const endTime = new Date(props.item.bid_status.end_datetime)
    return now >= endTime
  })

  // Format end datetime display
  const getEndDateTimeDisplay = endDatetime => {
    if (!endDatetime) return ''

    const now = new Date()
    const endTime = new Date(endDatetime)
    const isJapanese = languageStore.currentLanguage === 'ja'

    // Check if auction has ended
    if (now >= endTime) {
      return isJapanese ? 'オークション終了' : 'Auction end'
    }

    const {datePart, timePart} = formatDateString(endDatetime)
    const ja = `（${datePart} ${timePart} ${translate('SCHEDULED_END')}）`
    const en = `（${translate('SCHEDULED_END')} ${datePart} ${timePart}）`
    return isJapanese ? ja : en
  }

  // Handle bid price increment
  const incrementBidPrice = (currentPrice, increment) => {
    const current = parseInt(currentPrice?.replace(/[^0-9]/g, '') || '0')
    bidPrice.value = (current + increment).toLocaleString()
  }

  // Compute remaining time display based on remaining_seconds
  // Output:日本語：残り2日20時間または残り４時間、英語：remaining 2 days 20 hours or remaining 4 hours
  const getRemainingTimeDisplay = computed(() => {
    const remainingSeconds = props.item?.bid_status?.remaining_seconds
    if (!remainingSeconds || remainingSeconds <= 0) return ''

    const totalSeconds = Math.floor(remainingSeconds)
    const days = Math.floor(totalSeconds / (24 * 3600))
    const hours = Math.floor((totalSeconds % (24 * 3600)) / 3600)

    const isJapanese = languageStore.currentLanguage === 'ja'

    if (days >= 1) {
      // Show days and hours when >= 1 day
      return isJapanese
        ? `残り${days}日${hours}時間`
        : `remaining ${days} day${days > 1 ? 's' : ''} ${hours} hour${hours !== 1 ? 's' : ''}`
    } else {
      // Show hours only when < 1 day (round up to next hour)
      const totalHours = Math.ceil(totalSeconds / 3600)
      return isJapanese ? `残り${totalHours}時間` : `remaining ${totalHours} hours`
    }
  })

  const ableToCancelBid = computed(
    () =>
      props.item?.bid_status?.bid_price > 0 &&
      props.item?.bid_status?.can_bid &&
      props.item?.attention_info?.isCancelBidAllowed
  )

  const buildBidParams = item => {
    return {
      exhibitionNo: item.exhibition_no,
      exhibitionItemNo: item.exhibition_item_no,
      // attention info
      viewCount: item.attention_info?.view_count,
      favCount: item.attention_info?.favorited_count,
      bidCount: item.attention_info?.bid_count,
      // exhibitionName: item.exhibition_name,
      exhibitionName: 'todo',
      lowestBidPrice: item.bid_status?.lowest_bid_price,
      lowestBidQuantity: item.bid_status?.lowest_bid_quantity || 1,
      pitchWidth: item.bid_status?.pitch_width || 1,
      freeField: item.free_field,
      newBidPrice: bidPrice.value,
      newBidQuantity: bidQuantity.value || '1',
      currentBidPrice: item.bid_status?.current_price,
      // enteredBidPrice: item.bid_status?.bid_price,
      // enteredBidQuantity: item.bid_status?.bid_quantity,
      enteredBidPrice: 100,
      enteredBidQuantity: 1,
      maxQuantity: item.bid_status?.quantity,
      isAscendingAuction: false, // StandardAuctionItem is for sealed auctions
      hasUserBid: item.bid_status?.has_user_bid || false,
      nyusatsu_seigen: item.attention_info?.nyusatsu_seigen,
      bidStatus: item.bid_status,
      attention_info: item.attention_info,
    }
  }

  // Generic handler(action: 'bid' | 'cancel')
  const handleBidAction = (item, action) => {
    if (action === 'bid') {
      if (!bidPrice.value) {
        console.log('❌️ No bid price entered!')
        return
      }
      if (!bidQuantity.value) {
        bidQuantity.value = '1'
      }
    }

    const bidParams = buildBidParams(item)

    if (action === 'bid') {
      bidHandle(bidParams)
    } else {
      bidCancelHandle(bidParams)
    }
  }

  const handleCountdownEnd = () => {
    console.log('Auction countdown ended')
    // TODO: Refresh the items to get updated status
  }
</script>

<template>
  <div class="item-list row-bid">
    <ul>
      <li :class="getAuctionStatusClass(item)">
        <figure>
          <img :src="getImageUrl(item?.images)" />
          <div class="tab-f"><span class="title-a">New</span></div>
        </figure>

        <div class="item-p-desc">
          <p class="item-name">
            <a @click="navigateToPath(item?.link, goToPath, $route)">
              {{ item?.free_field?.product_name || '' }}
              <span class="tab-item" v-if="item?.free_field?.shipping_free"> 送料無料 </span>
            </a>
          </p>

          <div class="summary-wrap">
            <!-- 競り上がりの場合 -->
            <template v-if="item?.auction_classification == '1'">
              <div class="desc-p-top">
                <div class="price-box">
                  <p class="price">
                    <span class="price-c">{{ translate('LIST_CURRENT') }}</span>
                    <span class="price-v">{{
                      priceLocaleString(item?.bid_status?.current_price) || '0'
                    }}</span>
                    <span class="price-u">{{ translate('DETAIL_CURRENCY') }}</span>
                  </p>
                  <p class="price" v-if="item?.free_field?.deal_bid_price">
                    <span class="price-c">{{ translate('DETAIL_DEAL_BID_PRICE') }}</span>
                    <span class="price-v bl">{{
                      priceLocaleString(item?.free_field?.deal_bid_price)
                    }}</span>
                    <span class="price-u bl">{{ translate('DETAIL_CURRENCY') }}</span>
                  </p>
                  <p class="price" v-if="item?.bid_status?.lowest_bid_price">
                    <span class="price-c">{{ translate('LIST_LOWEST_BID_PRICE') }}</span>
                    <span class="price-v bl sm">{{
                      priceLocaleString(item?.bid_status?.lowest_bid_price)
                    }}</span>
                    <span class="price-u bl sm">{{ translate('DETAIL_CURRENCY') }}</span>
                  </p>
                </div>
                <ul class="tab-wrap-status">
                  <li class="top" v-if="item?.bid_status?.is_top_member">
                    {{ translate('YOU_ARE_TOP') }}
                  </li>
                  <li class="min-bid" v-if="item?.bid_status?.minimum_bid_exceeded">
                    {{ translate('RESERVE_PRICE_MET') }}
                  </li>
                </ul>
              </div>

              <ul class="tab-wrap" v-if="item?.free_field">
                <li class="tab-main" v-if="item?.free_field?.condition">
                  {{ item?.free_field?.condition }}
                </li>
                <li class="tab-sub" v-if="item?.free_field?.maker">
                  {{ item?.free_field?.maker }}
                </li>
                <li class="tab-wari" v-if="item?.free_field?.color">
                  {{ item?.free_field?.color }}
                </li>
              </ul>

              <ul class="pre-bid">
                <li class="view">
                  <p>{{ item?.attention_info?.view_count || 0 }}</p>
                </li>
                <li class="favo">
                  <p>{{ item?.attention_info?.favorited_count || 0 }}</p>
                </li>
                <li class="bid-v">
                  <p>{{ item?.attention_info?.bid_count || 0 }}</p>
                </li>
                <li class="end-v">
                  <p>
                    <span class="date red" v-if="getRemainingTimeDisplay">
                      {{ getRemainingTimeDisplay }}
                    </span>
                    <span class="date red" v-if="!isEndAuction && item?.bid_status?.extending">
                      <ExtendCountDown
                        :remainingSeconds="item?.bid_status?.remaining_seconds"
                        @countdown-ended="handleCountdownEnd"
                      />
                    </span>
                    <span class="end">
                      {{ getEndDateTimeDisplay(item?.bid_status?.end_datetime) }}
                    </span>
                  </p>
                </li>
              </ul>
            </template>
            <!-- 封印の場合 -->
            <template v-else>
              <div class="summary-wrap">
                <div class="desc-p-top">
                  <div class="price-box">
                    <p class="price">
                      <span class="price-c">{{ translate('LIST_LOWEST_BID_PRICE') }}</span
                      ><span class="price-v bl">
                        {{ priceLocaleString(item?.bid_status?.lowest_bid_price) }} </span
                      ><span class="price-u bl">{{ translate('DETAIL_CURRENCY') }}</span>
                    </p>
                  </div>
                </div>
                <ul class="tab-wrap">
                  <li class="tab-main">未使用に近い</li>
                  <li class="tab-sub">ICON</li>
                  <li class="tab-wari">ICON</li>
                </ul>
              </div>
              <ul class="pre-bid sealed">
                <li class="end-label">{{ translate('LIST_END_DATE_PLAN') }}</li>
                <li class="end-v">
                  <p>
                    <span class="date">
                      {{ formatDateString(item?.bid_status?.end_datetime).datePart }}
                    </span>
                    <span class="time" style="margin-left: 4px">
                      {{ formatDateString(item?.bid_status?.end_datetime).timePart }}
                    </span>
                  </p>
                </li>
              </ul>
            </template>
            <div class="btn-foreground-wrap">
              <button class="btn refresh" @click="emit('refresh')"></button>
              <button
                class="btn favorite"
                :class="{active: item?.attention_info?.is_favorited}"
                @click="toggleFavorite(item?.exhibition_item_no)"
              ></button>
            </div>
          </div>
        </div>

        <div class="place-bid">
          <div class="price">
            <span class="ttl">{{ translate('NYUSATSU_KAKAKU') }}</span>
            <input
              type="text"
              data-id="price-bid"
              v-model="bidPrice"
              class="price-bid"
              :placeholder="priceLocaleString(item?.bid_status?.current_price) || '1,000'"
            />
            {{ translate('DETAIL_CURRENCY') }}
          </div>
          <ul class="bidding-unit">
            <li>
              <button class="bid-unit" @click="incrementBidPrice(bidPrice, 10000)">
                <span class="icn_add"></span>
                {{
                  languageStore.currentLanguage === 'ja'
                    ? `10,000 ${translate('DETAIL_CURRENCY')}`
                    : `10,000 ${translate('DETAIL_CURRENCY')}`
                }}
              </button>
            </li>
            <li>
              <button class="bid-unit" @click="incrementBidPrice(bidPrice, 50000)">
                <span class="icn_add"></span>
                {{
                  languageStore.currentLanguage === 'ja'
                    ? `50,000 ${translate('DETAIL_CURRENCY')}`
                    : `50,000 ${translate('DETAIL_CURRENCY')}`
                }}
              </button>
            </li>
            <li>
              <button class="bid-unit" @click="incrementBidPrice(bidPrice, 100000)">
                <span class="icn_add"></span>
                {{
                  languageStore.currentLanguage === 'ja'
                    ? `100,000 ${translate('DETAIL_CURRENCY')}`
                    : `100,000 ${translate('DETAIL_CURRENCY')}`
                }}
              </button>
            </li>
          </ul>
          <div class="button-bid">
            <button class="btn" @click="handleBidAction(item, 'bid')" :disabled="!bidPrice">
              <img class="pct" src="@/assets/img/common/icn_bid_w.svg" />
              <span class="bid">{{ translate('FAVORITE_BID_BUTTON') }}</span>
            </button>
          </div>
          <div
            class="button-bid-cancel"
            v-if="ableToCancelBid"
            @click="handleBidAction(item, 'cancel')"
          >
            <button class="btn modal-open">
              <span class="bid">{{ translate('DETAIL_CANCEL_BID') }}</span>
            </button>
          </div>
        </div>
      </li>
    </ul>
  </div>
</template>

<style lang="css" scoped>
  .price-bid {
    background: white;
  }
  .button-bid .btn {
    cursor: pointer;
  }
</style>
