<script setup lang="ts">
  import type {ReactiveProductDetails} from '@/composables/_type'
  import useBid from '@/composables/bid'
  import {calculatePriceWithTax, formatDateString, priceLocaleString} from '@/composables/common'
  import useFavorite from '@/composables/favorite'
  import useGetItemDetails from '@/composables/getItemDetails'
  import useApi from '@/composables/useApi'
  import useAuctionStatus from '@/composables/useAuctionStatus'
  import {useLanguageRefetch} from '@/composables/useLanguageRefetch.ts'
  import {PATH_NAME} from '@/defined/const'
  import {useTypedI18n} from '@/language/index.ts'
  import {useBidConfirmStore} from '@/stores/bidConfirm'
  import {useCognitoAuthStore} from '@/stores/cognitoAuth.ts'
  import {useLanguageStore} from '@/stores/language.js'
  import useGoBack, {usePrevRouteStore} from '@/stores/prev-route'
  import {useSearchResultStore} from '@/stores/search-results'
  import {eventBus, navigateToPath} from '@/utils/index.js'
  import {format} from 'date-fns'
  import {
    computed,
    defineAsyncComponent,
    onBeforeMount,
    onMounted,
    onUnmounted,
    ref,
    watchEffect,
  } from 'vue'
  import {RouterLink, useRoute, useRouter} from 'vue-router'
  import ImageGallery from './ImageGallery.vue'
  import {buildGalleryImages} from './utils.ts'

  const BidConfirmModal = defineAsyncComponent(() => import('../../common/BidConfirmModal.vue'))
  const ExtendCountDown = defineAsyncComponent(() => import('../../parts/ExtendCountDown.vue'))

  const isLoading = ref(true)
  const isManageNo = ref(null)

  const route = useRoute()
  const auth = useCognitoAuthStore()
  const router = useRouter()
  const languageStore = useLanguageStore()
  const {goToPath} = usePrevRouteStore()
  const {apiExecute} = useApi()
  const {getItemDetailByExhItemNo, getConstants} = useGetItemDetails()
  const {t: translate} = useTypedI18n()
  const {refetchOnLanguageChange} = useLanguageRefetch()
  const {goBack} = useGoBack()

  // Additional stores and composables for bidding functionality
  const {productDetails, setProductListForContact} = useSearchResultStore()
  const {setPrevRoute} = usePrevRouteStore()
  const {getAuctionStatusClass} = useAuctionStatus()
  const {increaseFavoriteCount, decreaseFavoriteCount} = useFavorite()
  const {
    priceMaxLength,
    bidQuantity,
    bidPrice,
    addPitch,
    bidHandle,
    bidCancelHandle,
    clearBidInput,
  } = useBid()
  const {toggleFavorite} = useFavorite()

  const bidConfirmStore = useBidConfirmStore()

  const itemDetail = computed(() => productDetails as ReactiveProductDetails)
  const galleryImages = computed(() => buildGalleryImages(itemDetail.value?.images))
  console.log(
    '%c ◼️: itemDetail ',
    'font-size:16px;background-color:#00ae4b;color:white;',
    itemDetail
  )

  watchEffect(() => {
    console.log(
      '%c 🚵‍♀️: itemDetail.value?.images ',
      'font-size:16px;background-color:#1ea484;color:white;',
      itemDetail.value?.images
    )
    console.log(
      '%c 🇲🇪: galleryImages ',
      'font-size:16px;background-color:#6716e8;color:white;',
      galleryImages
    )
  })
  // Reactive variables for bidding
  const canBid = computed(
    () => itemDetail.value?.bid_status?.can_bid === true && !itemDetail.value?.bid_status?.sold_out
  )
  const ableToCancelBid = computed(
    () =>
      itemDetail.value?.bid_status?.bid_price > 0 &&
      itemDetail.value?.bid_status?.can_bid &&
      itemDetail.value?.attention_info?.isCancelBidAllowed
  )

  const isEndAuction = computed(() => {
    const bidStatus = itemDetail.value?.bid_status
    if (!bidStatus?.end_datetime) return false

    const {deal_bid_price: dealPrice, top_price: topPrice, end_datetime} = bidStatus

    // Case 1: Ended by reaching deal price
    if (dealPrice > 0 && topPrice >= dealPrice) return true

    // Case 2: Ended by time expiration
    return new Date() >= new Date(end_datetime)
  })

  // Format bid history datetime based on current language
  // TODO: Implement return all history from database
  const formatBidHistoryDateTime = (dateTimeString: string) => {
    if (!dateTimeString) return {datePart: '', timePart: ''}

    const currentLang = languageStore.language
    const dateObj = new Date(dateTimeString)

    if (isNaN(dateObj.getTime())) {
      return {datePart: 'Invalid Date', timePart: 'Invalid Time'}
    }

    if (currentLang === 'ja') {
      const datePart = format(dateObj, 'M月d日')
      const timePart = format(dateObj, 'H時m分')
      return {datePart, timePart}
    } else {
      // English format
      const datePart = format(dateObj, 'M/d')
      const timePart = format(dateObj, 'H:mm')
      return {datePart, timePart}
    }
  }

  // 初期表示処理
  const initItemDetail = async () => {
    isLoading.value = true
    isManageNo.value = route.params.manageNo

    // TODO delete makeshop logic???
    const isFromMakeshop = !!route.query?.brand_code
    await Promise.all([
      auth.isAuthenticated ? apiExecute('private/get-change-info-constants') : null,
      getConstants(),
      await getItemDetailByExhItemNo(isManageNo.value ?? '', isFromMakeshop ? 'makeshop' : null),
    ])
    if (itemDetail.value?.exhibition_item_no) {
      // Makeshopからアクセスがあった場合、該当の商品コードでオークション中の商品が無い場合は商品一覧に遷移させる
      if (isFromMakeshop && !itemDetail.value.bid_status.can_bid) {
        router.push(PATH_NAME.TOP)
        return
      }
      setProductListForContact()
      // if (auth.isAuthenticated) {
      //   member.setMemberInfo(responses[0])
      // }
      // Initiate bid price when page is loaded
      if (itemDetail.value.bid_status) {
        // Set bidPrice to the current bid_price, or empty string if null
        bidPrice.value = itemDetail.value.bid_status.bid_price
          ? priceLocaleString(itemDetail.value.bid_status.bid_price)
          : ''
        // 競り上げの場合は「bidQuantity = ’1’」, 封印入札の場合は「bidQuantity = bid_status.bid_quantity」
        bidQuantity.value = '1'
      }
    } else {
      router.push(PATH_NAME.TOP)
    }
    isLoading.value = false
  }

  // Handle countdown end event
  const handleCountdownEnd = () => {
    console.log('Auction countdown ended')
    // Refresh the item details to get updated status
    initItemDetail()
  }

  const handleFavorite = () => {
    toggleFavorite(
      itemDetail.value.exhibition_item_no,
      itemDetail.value.attention_info.is_favorited
    )
  }

  const handleBidPriceIncrement = (increment: number): void => {
    const currentPrice = itemDetail.value.bid_status?.current_price || 0
    const pitchWidth = itemDetail.value.bid_status?.pitch_width || 1
    addPitch(currentPrice, increment, pitchWidth)
  }

  const buildBidParams = () => {
    return {
      exhibitionNo: itemDetail.value.exhibition_no || '',
      exhibitionItemNo: itemDetail.value.exhibition_item_no || '',
      bidCount: itemDetail.value.attention_info?.bid_count || 0,
      viewCount: itemDetail.value.attention_info?.view_count || 0,
      favCount: itemDetail.value.attention_info?.favorited_count || 0,
      exhibitionName:
        itemDetail.value.productName || itemDetail.value.freeFields?.product_name || '',
      lowestBidPrice: itemDetail.value.bid_status?.lowest_bid_price || 0,
      lowestBidQuantity: 1,
      pitchWidth: itemDetail.value.bid_status?.pitch_width || 1,
      freeField: itemDetail.value.free_field || itemDetail.value.freeFields || {},
      newBidPrice: bidPrice.value,
      newBidQuantity: 1,
      currentBidPrice: itemDetail.value.bid_status?.current_price || 0,
      enteredBidPrice: itemDetail.value.bid_status?.bid_price || 0,
      enteredBidQuantity: 1,
      maxQuantity: 1,
      isAscendingAuction: itemDetail.value.auction_classification === 1,
      hasUserBid: itemDetail.value.hasUserBid,
      nyusatsu_seigen: itemDetail.value.attention_info?.nyusatsu_seigen,
      bidStatus: itemDetail.value.bid_status,
      attention_info: itemDetail.value.attention_info,
    }
  }

  // Generic handler(action: 'bid' | 'cancel')
  const handleBidAction = (action: 'bid' | 'cancel') => {
    if (action === 'bid') {
      if (!bidPrice.value) {
        console.log('❌️No bid price entered!')
        return
      }
    }

    const bidParams = buildBidParams()
    if (action === 'bid') {
      bidHandle(bidParams)
    } else {
      bidCancelHandle(bidParams)
    }
  }

  // Handle refresh after bid confirmation modal closes
  const reInitDetailAfterBid = (classification?: any): void => {
    console.log('Refreshing after bid confirmation', classification)
    initItemDetail()
  }

  const itemTags = computed(
    () =>
      itemDetail.value?.mappingFreeFields?.filter(
        item => item.display_area === '商品詳細（タグ）'
      ) ?? []
  )

  const mappingFreeFieldsArrays = computed(() => {
    if (!itemDetail.value?.mappingFreeFields) return []
    if (Array.isArray(itemDetail.value.mappingFreeFields)) {
      return itemDetail.value.mappingFreeFields.filter(
        item =>
          item.display_area === '商品詳細（表）' && item.value !== undefined && item.value !== null
      )
    }
  })

  const handleAfterBidSuccess = async bidSuccessPrice => {
    console.log('bidSuccessPrice: ', bidSuccessPrice)
  }

  const handleUpdate = async () => {
    await initItemDetail()
  }

  const formattedDescription = computed(() => {
    const raw = itemDetail.value?.freeFields?.description || '商品説明がありません。'
    return raw.replace(/\n/g, '<br>')
  })

  onBeforeMount(async () => {
    await initItemDetail()
  })

  onMounted(() => {
    eventBus.on('onBidSuccess', (bidItemNo, bidSuccessPrice) => {
      if (itemDetail.value.exhibition_item_no === bidItemNo) {
        handleAfterBidSuccess(bidSuccessPrice)
      }
    })
    // Set up language change watcher
    refetchOnLanguageChange(initItemDetail)
  })

  onUnmounted(() => {
    eventBus.off('onBidSuccess', handleAfterBidSuccess)
  })
</script>

<template>
  <main id="main">
    <div id="pNav">
      <ul>
        <li><RouterLink :to="PATH_NAME.TOP">TOP</RouterLink></li>
        <li>
          <RouterLink :to="PATH_NAME.SEARCH_RESULTS">{{
            itemDetail?.freeFields?.category || translate('FILTER_BOX_CATEGORY')
          }}</RouterLink>
        </li>
        <li>
          {{ itemDetail?.productName || itemDetail?.freeFields?.product_name || '' }}
        </li>
      </ul>
    </div>
    <section id="item-detail">
      <div class="back">
        <button @click="goBack" class="text">{{ translate('DETAIL_BACK_TO_LIST') }}</button>
      </div>
      <div class="container">
        <p class="item-name-wrap">
          <span class="name">
            {{ itemDetail?.freeFields?.product_name }}
          </span>
          <!--TODO：管理サイト設定により、表示する <span class="tag_status">新品未使用</span> -->
        </p>
        <div
          v-if="
            itemDetail.mappingFreeFields?.some(
              item =>
                item.display_area === '商品詳細（タグ）' &&
                item.value !== null &&
                item.value !== '' &&
                item.value !== undefined
            )
          "
        >
          <ul class="tab-wrap" v-for="item in itemTags" :key="item.value">
            <li class="tab-sub">{{ item.value }}</li>
          </ul>
        </div>
        <div id="item-data">
          <div class="item_d-main">
            <div class="item_d-main-visual">
              <ImageGallery
                :images="galleryImages"
                gallery-class="my-gallery"
                slider-main-class="slider-for"
                slider-nav-class="slider-nav"
                :nav-slides-to-show="7"
                :nav-responsive="[{breakpoint: 767, settings: {slidesToShow: 4}}]"
              />

              <!-- 「入札履歴」は競り上がりオークションのみ表示する -->
              <div
                class="item_d-bid-history only_pc"
                v-if="
                  itemDetail.auction_classification == 1 &&
                  itemDetail.bid_histories &&
                  itemDetail.bid_histories.length > 0
                "
              >
                <p>{{ translate('DETAIL_BID_HISTORY') }}</p>
                <table cellspacing="0" cellpadding="0">
                  <tbody>
                    <tr>
                      <th class="time">{{ translate('DETAIL_LAST_BID_TIME') }}</th>
                      <th class="amount">{{ translate('DETAIL_LAST_BID_AMOUNT') }}</th>

                      <th class="bidder"></th>
                    </tr>
                    <tr v-for="(bid, index) in itemDetail.bid_histories" :key="index">
                      <td class="time">
                        <span>{{ formatBidHistoryDateTime(bid.create_datetime).datePart }}</span>
                        <span>{{ formatBidHistoryDateTime(bid.create_datetime).timePart }}</span>
                      </td>
                      <td class="amount">
                        {{ priceLocaleString(bid.bid_price) }}{{ translate('DETAIL_CURRENCY') }}
                      </td>

                      <td class="bidder">
                        <span class="highest" v-if="index === 0">{{
                          translate('DETAIL_HIGHEST_BID_AMOUNT')
                        }}</span>
                        <span v-else-if="bid.nickname">{{ bid.nickname }}</span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <div class="item_d-main-txt">
              <div class="item_d-main-data">
                <div class="bid-mode">
                  <p class="mode-name">
                    <span>
                      {{
                        itemDetail?.auction_classification === 1
                          ? translate('ASCENDING_AUCTION')
                          : translate('SEALED_AUCTION')
                      }}
                    </span>
                  </p>
                  <p class="update" @click="handleUpdate">
                    <span>{{ translate('COMMON_UPDATE_AUCTION') }}</span>
                  </p>
                </div>
                <dl class="bid-price">
                  <template v-if="itemDetail?.auction_classification === 1">
                    <dd class="price-start">
                      <span class="label">{{ translate('DETAIL_INFO_START_PRICE') }}</span>
                      <span class="value">
                        {{ priceLocaleString(itemDetail?.bid_status?.lowest_bid_price) }}
                        <span class="unit">{{ translate('DETAIL_CURRENCY') }}</span>
                      </span>
                    </dd>
                    <dd class="price-now">
                      <span class="label">{{ translate('DETAIL_INFO_CURRENT_PRICE') }}</span>
                      <span class="value">
                        {{ priceLocaleString(itemDetail?.bid_status?.current_price) }}
                        <span class="unit">{{ translate('DETAIL_CURRENCY') }}</span>
                      </span>
                    </dd>
                  </template>
                  <template v-else>
                    <dd class="min">
                      <span class="label">{{ translate('LIST_LOWEST_BID_PRICE') }}</span>
                      <span class="value">
                        {{ priceLocaleString(itemDetail?.bid_status?.lowest_bid_price) }}
                        <span class="unit">{{ translate('DETAIL_CURRENCY') }}</span>
                      </span>
                    </dd>
                  </template>
                  <dd class="tax-include">
                    <span class="label">{{ translate('DETAIL_INFO_TAX_INCLUDED_PRICE') }}</span>
                    <span class="value">
                      {{
                        priceLocaleString(
                          calculatePriceWithTax(
                            itemDetail?.bid_status?.current_price,
                            itemDetail?.bid_status?.tax_rate
                          )
                        )
                      }}
                      <span class="unit">{{ translate('DETAIL_CURRENCY') }}</span>
                    </span>
                  </dd>
                  <dd class="price-buyit" v-if="itemDetail?.bid_status?.deal_bid_price > 0">
                    <span class="label">{{ translate('DETAIL_DEAL_BID_PRICE') }}</span>
                    <span class="value" v-if="itemDetail?.bid_status?.deal_bid_price">
                      {{ priceLocaleString(itemDetail?.bid_status?.deal_bid_price) }}
                      <span class="unit">{{ translate('DETAIL_CURRENCY') }}</span>
                    </span>
                  </dd>
                </dl>
                <div class="bid-status">
                  <!-- 入札期間中 -->
                  <span class="during" v-if="canBid">{{ translate('DURING_BIDDING_PERIOD') }}</span>
                  <!-- あなたはTOP入札者ではありません -->
                  <span class="lose" v-if="itemDetail?.bid_status?.is_top_member === false">
                    {{ translate('YOU_ARE_NOT_TOP') }}
                  </span>
                  <!-- 最低落札価格に達していません -->
                  <span
                    class="reserve"
                    v-if="itemDetail?.bid_status?.is_not_exceeding_lowest_price"
                  >
                    {{ translate('RESERVE_PRICE_NOT_MET') }}
                  </span>
                  <!-- 最低落札価格に達しました -->
                  <span class="reserve" v-if="itemDetail?.bid_status?.is_exceeding_lowest_price">
                    {{ translate('RESERVE_PRICE_MET') }}
                  </span>
                  <!-- オークション延長中 -->
                  <span class="extended" v-if="itemDetail?.bid_status?.extending && !isEndAuction">
                    {{ translate('BID_STATUS_EXTENDING') }}
                  </span>
                  <!-- あなたがTOP -->
                  <span class="top" v-if="itemDetail?.bid_status?.is_top_member">
                    {{ translate('YOU_ARE_TOP') }}
                  </span>
                  <!-- もう少しで最低落札価格です -->
                  <span class="more-little" v-if="itemDetail?.bid_status?.is_more_little">
                    {{ translate('MORE_LITTLE') }}
                  </span>
                  <!-- オークション終了 -->
                  <span class="extended" v-if="isEndAuction">
                    {{ translate('BID_STATUS_ENDED') }}
                  </span>
                  <!-- 下見期間中 -->
                  <span
                    class="pre-auction"
                    v-if="Date.now() < Date.parse(itemDetail?.bid_status?.start_datetime)"
                  >
                    {{ translate('BID_STATUS_PRE_AUCTION') }}
                  </span>
                </div>
              </div>
              <div
                class="item_d-bid-history only_sp"
                v-if="itemDetail.bid_histories && itemDetail.bid_histories.length > 0"
              >
                <p>{{ translate('DETAIL_BID_HISTORY') }}</p>
                <table cellspacing="0" cellpadding="0">
                  <tbody>
                    <tr>
                      <th class="time">{{ translate('DETAIL_LAST_BID_TIME') }}</th>
                      <th class="amount">{{ translate('DETAIL_LAST_BID_AMOUNT') }}</th>
                      <th class="bidder"></th>
                    </tr>
                    <tr v-for="(bid, index) in itemDetail.bid_histories" :key="index">
                      <td class="time">
                        <span>{{ formatBidHistoryDateTime(bid.create_datetime).datePart }}</span>
                        <span>{{ formatBidHistoryDateTime(bid.create_datetime).timePart }}</span>
                      </td>
                      <td class="amount">
                        {{ priceLocaleString(bid.bid_price) }}{{ translate('DETAIL_CURRENCY') }}
                      </td>

                      <td class="bidder">
                        <span class="highest" v-if="index === 0">{{
                          translate('HIGHEST_BID_AMOUNT')
                        }}</span>
                        <span v-else-if="bid.nickname">{{ bid.nickname }}</span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <div class="place-bid" :class="{view_only: !canBid}">
                <dl class="bidded-price">
                  <dt>{{ translate('RECORDED_BID_PRICE') }}</dt>
                  <dd>
                    <span class="price">
                      {{ priceLocaleString(itemDetail?.bid_status?.bid_price) }}
                      <span class="unit">{{ translate('DETAIL_CURRENCY') }}</span>
                    </span>
                  </dd>
                </dl>
                <div class="bid_head">
                  <p class="ttl">
                    <span>{{ translate('NYUSATSU_KAKAKU') }}</span>
                  </p>
                  <p class="price">
                    <input
                      type="text"
                      data-id="price-bid"
                      v-model="bidPrice"
                      :class="{
                        'price-bid': true,
                        'bg-white': canBid,
                        'bg-surface-light': !canBid,
                      }"
                      :placeholder="
                        priceLocaleString(itemDetail?.bid_status?.current_price) || '1,000'
                      "
                      @input="bidPrice = bidPrice?.replace(/[^0-9]/g, '')?.slice(0, priceMaxLength)"
                      @blur="
                        () => {
                          bidPrice = priceLocaleString(bidPrice)
                        }
                      "
                    /><span class="unit">{{ translate('DETAIL_CURRENCY') }}</span>
                  </p>
                </div>
                <ul class="bid-unit-wrap" :class="{'cannot-click': !canBid}">
                  <li>
                    <button class="bid-unit" @click="handleBidPriceIncrement(10000)">
                      <span class="icn_add"></span>10,000{{ translate('DETAIL_CURRENCY') }}
                    </button>
                  </li>
                  <li>
                    <button class="bid-unit" @click="handleBidPriceIncrement(50000)">
                      <span class="icn_add"></span>50,000{{ translate('DETAIL_CURRENCY') }}
                    </button>
                  </li>
                  <li class="last">
                    <button class="bid-unit" @click="handleBidPriceIncrement(100000)">
                      <span class="icn_add"></span>100,000{{ translate('DETAIL_CURRENCY') }}
                    </button>
                  </li>
                </ul>
                <p class="note">※{{ translate('UNIT_BID_AVAILABLE') }}</p>
                <div class="btn-wrap">
                  <button
                    class="btn modal-open"
                    @click="handleBidAction('bid')"
                    :class="{'cannot-click background-gray': !canBid}"
                    :disabled="!bidPrice"
                  >
                    <img class="pct" src="@/assets/img/common/icn_bid_detail.svg" /><span
                      class="bid-text"
                    >
                      {{ translate('DETAIL_BID_BUTTON') }}
                    </span>
                  </button>
                  <button
                    class="btn cancel modal-open"
                    v-if="ableToCancelBid"
                    @click="handleBidAction('cancel')"
                  >
                    <span class="bid-text">{{ translate('DETAIL_CANCEL_BID') }}</span>
                  </button>

                  <button
                    class="btn chat"
                    @click="
                      navigateToPath(
                        '/details/chat/' + itemDetail.exhibition_item_no,
                        goToPath,
                        $route
                      )
                    "
                  >
                    <img class="pct" src="@/assets/img/common/icn_chat_detail.svg" />
                    <span class="text">{{ translate('DETAIL_CHAT') }}</span>
                  </button>
                  <a
                    class="view_comment"
                    @click="
                      navigateToPath(
                        '/details/chat/' + itemDetail.exhibition_item_no,
                        goToPath,
                        $route
                      )
                    "
                  >
                    {{ translate('DETAIL_VIEW_COMMENTS') }}
                  </a>
                </div>

                <div class="current-status">
                  <div class="period">
                    <img src="@/assets/img/common/icn_schedule.svg" />
                    <div class="text-wrap">
                      <div class="end-l">{{ translate('DETAIL_BID_PERIOD') }}</div>
                      <div class="end-v">
                        <span>
                          {{ formatDateString(itemDetail?.bid_status?.start_datetime).datePart }}
                          {{ formatDateString(itemDetail?.bid_status?.start_datetime).timePart }}
                        </span>
                        <span class="mark">〜</span>
                        <span>
                          {{ formatDateString(itemDetail?.bid_status?.end_datetime).datePart }}
                          {{ formatDateString(itemDetail?.bid_status?.end_datetime).timePart }}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div class="end-date">
                    <img src="@/assets/img/common/icn_clock_list.png" />
                    <div class="end-l">{{ translate('SCHEDULED_END') }}</div>
                    <div class="end-v">
                      <span>
                        {{ formatDateString(itemDetail?.bid_status?.end_datetime).datePart }}
                      </span>
                      <span>
                        {{ formatDateString(itemDetail?.bid_status?.end_datetime).timePart }}
                      </span>
                      <span
                        class="countdown-inline"
                        v-if="!isEndAuction && itemDetail?.bid_status?.extending"
                      >
                        <ExtendCountDown
                          :remainingSeconds="itemDetail?.bid_status?.remaining_seconds"
                          @countdown-ended="handleCountdownEnd"
                        />
                      </span>
                    </div>
                  </div>
                  <div class="other-info">
                    <div class="view">
                      <img src="@/assets/img/common/icn_eye_list.svg" /><span>{{
                        itemDetail?.attention_info?.view_count
                      }}</span>
                    </div>
                    <div class="favorite">
                      <img src="@/assets/img/common/icn_favorite_detail.svg" /><span>{{
                        itemDetail?.attention_info?.favorited_count || 0
                      }}</span>
                    </div>
                    <div class="bid">
                      <img src="@/assets/img/common/icn_bid.svg" /><span>{{
                        itemDetail?.attention_info?.bid_count
                      }}</span>
                    </div>
                  </div>
                </div>
              </div>
              <div class="com-item-box">
                <div id="sns-share">
                  <p>{{ translate('SHARE_THIS_PRODUCT') }}</p>
                  <ul>
                    <li>
                      <a href="A"
                        ><img
                          src="@/assets/img/common/icn_sns_facebook.svg"
                          alt="Facebook"
                          class="facebook"
                      /></a>
                    </li>
                    <li>
                      <a href="A"
                        ><img src="@/assets/img/common/icn_sns_x.svg" alt="twitter" class="x"
                      /></a>
                    </li>
                    <li>
                      <a href="A"
                        ><img
                          src="@/assets/img/common/icn_sns_instagram.svg"
                          alt="Instagram"
                          class="instagram"
                      /></a>
                    </li>
                  </ul>
                </div>
                <p
                  class="fav-mark"
                  :class="{active: itemDetail?.attention_info?.is_favorited}"
                  @click="handleFavorite"
                >
                  <span>{{ translate('DETAIL_INFO_FAVORITE') }}</span>
                </p>
              </div>
            </div>
          </div>
        </div>
        <div class="item-note">
          <h2>{{ translate('DETAIL_DESCRIPTION') }}</h2>
          <div class="contents-wrap">
            <div v-html="formattedDescription"></div>
            <table class="spec">
              <tbody>
                <tr v-for="field in mappingFreeFieldsArrays" :key="field.label">
                  <th>{{ field.label }}</th>
                  <td v-html="field.value"></td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <BidConfirmModal
        v-model="bidConfirmStore.showBidConfirm"
        :isAscendingAuction="itemDetail?.auction_classification === 1"
        @refresh="reInitDetailAfterBid"
      />
    </section>
  </main>
</template>

<style lang="css" scoped>
  .bid-status {
    display: flex;
    gap: 8px;
  }

  .thumbnail-item {
    padding: 0 3px !important;
  }

  .auction-type-switch {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem 0;
    margin-bottom: 2rem;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
  }

  .switch-label {
    margin-right: 1rem;
    font-weight: 500;
    color: #333;
    font-size: 1rem;
  }

  .switch-buttons {
    display: flex;
    background-color: #fff;
    border: 2px solid #427fae;
    border-radius: 25px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(66, 127, 174, 0.1);
  }

  .switch-btn {
    padding: 0.5rem 1.5rem;
    border: none;
    background-color: #fff;
    color: #427fae;
    font-weight: 500;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
  }

  .switch-btn:hover:not(:disabled) {
    background-color: rgba(66, 127, 174, 0.1);
  }

  .switch-btn.is-active {
    background-color: #427fae;
    color: #fff;
    cursor: default;
  }

  .switch-btn:disabled {
    cursor: default;
  }

  /* Responsive design */
  @media screen and (max-width: 767px) {
    .auction-type-switch {
      flex-direction: column;
      align-items: center;
      padding: 1rem;
    }

    .switch-label {
      margin-right: 0;
      margin-bottom: 0.5rem;
      font-size: 0.9rem;
    }

    .switch-btn {
      padding: 0.4rem 1rem;
      font-size: 0.8rem;
    }
  }

  .view_only:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(240, 240, 240, 0.5);
    z-index: 10;
  }

  .back button.text {
    background: none;
    border: none;
    padding: 0;
    font: inherit;
    color: inherit;
    text-decoration: underline;
    cursor: pointer;
    outline: none;
  }

  .back button.text:hover {
    text-decoration: none;
  }
  .bg-white {
    background-color: #fff;
  }
  .bg-surface-light {
    cursor: default;
    background: rgba(240, 240, 240, 0.5);
    pointer-events: none;
  }
  .cannot-click {
    pointer-events: none;
  }
  .background-gray {
    background-color: rgb(158, 158, 158) !important;
  }

  .countdown-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 8px 16px;
    border-radius: 6px;
    margin: 8px 0;
  }

  .countdown-inline {
    display: inline-flex;
    align-items: center;
    margin-left: 8px;
  }
  .pre-auction {
    background-color: #82c251;
    display: inline-block;
    height: auto;
    padding: 0.5rem 1rem;
    color: #fff;
    font-size: 0.8rem;
    font-weight: 600;
    line-height: 1.2;
    border-radius: 4px;
  }
</style>
