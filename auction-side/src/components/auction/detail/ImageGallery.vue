<script setup>
  import $ from 'jquery'
  import {defineProps, onMounted, onUnmounted, ref, watch} from 'vue'

  // Local loading state for gallery initialization
  const isGalleryLoading = ref(true)
  const isGalleryInitialized = ref(false)

  // Props interface
  const props = defineProps({
    images: {
      type: Array,
      required: true,
      validator: images => {
        return images.every(img => typeof img === 'object' && img.large && img.thumb && img.size)
      },
    },
    galleryClass: {
      type: String,
      default: 'my-gallery',
    },
    sliderMainClass: {
      type: String,
      default: 'slider-for',
    },
    sliderNavClass: {
      type: String,
      default: 'slider-nav',
    },
    slideItemClass: {
      type: String,
      default: 'slide-item',
    },
    thumbnailItemClass: {
      type: String,
      default: 'thumbnail-item',
    },
    slidesToShow: {
      type: Number,
      default: 7,
    },
    slidesToShowMobile: {
      type: Number,
      default: 4,
    },
    mobileBreakpoint: {
      type: Number,
      default: 767,
    },
    initDelay: {
      type: Number,
      default: 300,
    },
  })

  // Load PhotoSwipe dynamically from public directory
  const loadPhotoSwipe = () => {
    return new Promise((resolve, reject) => {
      // Check if already loaded
      if (window.PhotoSwipe && window.PhotoSwipeUI_Default) {
        resolve()
        return
      }

      // Load PhotoSwipe core first
      const photoSwipeScript = document.createElement('script')
      photoSwipeScript.src = '/js/gallery/photoswipe.min.js'
      photoSwipeScript.onload = () => {
        // Then load PhotoSwipe UI
        const photoSwipeUIScript = document.createElement('script')
        photoSwipeUIScript.src = '/js/gallery/photoswipe-ui-default.js'
        photoSwipeUIScript.onload = () => resolve()
        photoSwipeUIScript.onerror = () => reject(new Error('Failed to load PhotoSwipe UI'))
        document.head.appendChild(photoSwipeUIScript)
      }
      photoSwipeScript.onerror = () => reject(new Error('Failed to load PhotoSwipe'))
      document.head.appendChild(photoSwipeScript)
    })
  }

  // Gallery initialization function
  const initializeGallery = async () => {
    isGalleryLoading.value = true
    isGalleryInitialized.value = false

    try {
      // Ensure DOM elements exist before initialization
      const $mainSlider = $(`.${props.sliderMainClass}`)
      const $navSlider = $(`.${props.sliderNavClass}`)

      if ($mainSlider.length === 0 || $navSlider.length === 0) {
        console.warn('Gallery DOM elements not found, skipping initialization')
        isGalleryLoading.value = false
        return
      }

      // Destroy existing sliders if they exist
      if ($mainSlider.hasClass('slick-initialized')) {
        $mainSlider.slick('unslick')
      }
      if ($navSlider.hasClass('slick-initialized')) {
        $navSlider.slick('unslick')
      }

      // Wait a tick for DOM to be ready
      await new Promise(resolve => setTimeout(resolve, 50))

      // Initialize Slick sliders
      $mainSlider.slick({
        asNavFor: `.${props.sliderNavClass}`,
        autoplay: false,
        arrows: true,
        infinite: true,
      })

      $navSlider.slick({
        slidesToShow: props.slidesToShow,
        slidesToScroll: 1,
        asNavFor: `.${props.sliderMainClass}`,
        focusOnSelect: true,
        responsive: [
          {
            breakpoint: props.mobileBreakpoint,
            settings: {
              slidesToShow: props.slidesToShowMobile,
            },
          },
        ],
      })

      // Load PhotoSwipe and initialize
      await loadPhotoSwipe()

      // Initialize PhotoSwipe
      const $gallery = document.querySelector(`.${props.galleryClass}`)
      const $pswpElement = document.querySelector('.pswp')

      if (!$gallery || !$pswpElement) {
        console.warn('Gallery elements not found')
        isGalleryLoading.value = false
        return
      }

      if (!window.PhotoSwipe || !window.PhotoSwipeUI_Default) {
        console.warn('PhotoSwipe not loaded')
        isGalleryLoading.value = false
        return
      }

      const items = Array.prototype.map
        .call($gallery.querySelectorAll('figure:not(.slick-cloned)'), $slide => {
          const $a = $slide.querySelector('a')

          // Skip if no anchor tag found
          if (!$a) {
            console.warn('No anchor tag found in slide item')
            return null
          }

          const sizeAttr = $a.getAttribute('data-size')

          // Provide default size if data-size is missing or null
          const size = sizeAttr ? sizeAttr.split('x') : ['1200', '1200']

          return {
            src: $a.getAttribute('href'),
            w: parseInt(size[0], 10),
            h: parseInt(size[1], 10),
            el: $slide,
          }
        })
        .filter(item => item !== null) // Filter out any null items

      // Check if we have any valid items
      if (items.length === 0) {
        console.warn('No valid gallery items found')
        isGalleryLoading.value = false
        return
      }

      const openPhotoSwipe = index => {
        const options = {
          index,
          history: false,
          getThumbBoundsFn(index) {
            const thumbnail = items[index].el.querySelector('img')
            const pageYScroll = window.pageYOffset || document.documentElement.scrollTop
            const elRect = thumbnail.getBoundingClientRect()

            return {
              x: elRect.left,
              y: elRect.top + pageYScroll,
              w: elRect.width,
            }
          },
        }

        const gallery = new window.PhotoSwipe(
          $pswpElement,
          window.PhotoSwipeUI_Default,
          items,
          options
        )
        gallery.init()
      }

      // Add click event listener for PhotoSwipe
      const slickList = $gallery.querySelector('.slick-list')
      if (slickList) {
        slickList.addEventListener('click', event => {
          event.preventDefault()
          const index = $(`.${props.sliderMainClass}`).slick('slickCurrentSlide')
          openPhotoSwipe(index)
        })
      }

      console.log('Gallery initialized successfully with PhotoSwipe')

      // Mark gallery as fully initialized and hide loading
      isGalleryInitialized.value = true
      isGalleryLoading.value = false
    } catch (error) {
      console.error('Failed to initialize gallery:', error)
      // Fallback: simple lightbox functionality
      const $gallery = document.querySelector(`.${props.galleryClass}`)
      if ($gallery) {
        $gallery.addEventListener('click', event => {
          const slideItem = event.target.closest(`.${props.slideItemClass}`)
          if (slideItem) {
            event.preventDefault()
            const link = slideItem.querySelector('a')
            if (link) {
              window.open(link.href, '_blank')
            }
          }
        })
      }
      isGalleryLoading.value = false
    }
  }

  // Cleanup function
  const destroyGallery = () => {
    try {
      // Destroy Slick sliders if they exist
      const $mainSlider = $(`.${props.sliderMainClass}`)
      const $navSlider = $(`.${props.sliderNavClass}`)

      if ($mainSlider.length > 0 && $mainSlider.hasClass('slick-initialized')) {
        $mainSlider.slick('unslick')
      }
      if ($navSlider.length > 0 && $navSlider.hasClass('slick-initialized')) {
        $navSlider.slick('unslick')
      }
    } catch (error) {
      console.warn('Error during gallery cleanup:', error)
    }
  }

  // Watch for image changes and reinitialize gallery
  watch(
    () => props.images,
    (newImages, oldImages) => {
      if (newImages !== oldImages && Array.isArray(newImages) && newImages.length > 0) {
        destroyGallery()
        setTimeout(() => {
          initializeGallery()
        }, 150)
      }
    },
    {deep: true}
  )

  // Vue lifecycle hooks
  onMounted(() => {
    // Wait for DOM to be fully rendered, then initialize gallery
    setTimeout(() => {
      initializeGallery()
    }, props.initDelay)
  })

  onUnmounted(() => {
    destroyGallery()
  })
</script>

<template>
  <div class="product-gallery">
    <!-- Loading State -->
    <div v-if="isGalleryLoading" class="gallery-loading">
      <div class="loading-spinner"></div>
    </div>

    <!-- Gallery Content - Only show when fully initialized -->
    <div v-show="isGalleryInitialized && !isGalleryLoading" class="slider_wrap">
      <div :class="[galleryClass, sliderMainClass]">
        <figure :class="slideItemClass" v-for="(image, index) in images" :key="index">
          <a :href="image.large" :data-size="image.size">
            <img :src="image.large" :alt="image.alt || 'Product Image'" />
          </a>
        </figure>
      </div>
      <ul :class="sliderNavClass">
        <li :class="thumbnailItemClass" v-for="(image, index) in images" :key="index">
          <img :src="image.thumb" :alt="image.alt || 'Product Thumbnail'" />
        </li>
      </ul>
    </div>

    <!-- PhotoSwipe Lightbox -->
    <div class="pswp" tabindex="-1" role="dialog" aria-hidden="true">
      <div class="pswp__bg"></div>
      <div class="pswp__scroll-wrap">
        <div class="pswp__container">
          <div class="pswp__item"></div>
          <div class="pswp__item"></div>
          <div class="pswp__item"></div>
        </div>
        <div class="pswp__ui pswp__ui--hidden">
          <div class="pswp__top-bar">
            <div class="pswp__counter"></div>
            <button class="pswp__button pswp__button--close" title="Close (Esc)"></button>
            <div class="pswp__preloader">
              <div class="pswp__preloader__icn">
                <div class="pswp__preloader__cut">
                  <div class="pswp__preloader__donut"></div>
                </div>
              </div>
            </div>
          </div>
          <button
            class="pswp__button pswp__button--arrow--left"
            title="Previous (arrow left)"
          ></button>
          <button
            class="pswp__button pswp__button--arrow--right"
            title="Next (arrow right)"
          ></button>
          <div class="pswp__caption">
            <div class="pswp__caption__center"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="css" scoped>
  .thumbnail-item {
    padding: 0 3px !important;
  }

  .product-gallery {
    width: 100%;
    position: relative;
    min-height: 400px; /* Ensure consistent height during loading */
  }

  .slider_wrap {
    width: 100%;
  }

  /* Loading State Styles */
  .gallery-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    border-radius: 8px;
    min-height: 400px;
    z-index: 10;
  }

  .loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
  }

  .loading-text {
    color: #666;
    font-size: 14px;
    margin: 0;
    font-weight: 500;
  }
</style>
