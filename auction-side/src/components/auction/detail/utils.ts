import {
  default as no_img01,
  default as no_img02,
  default as no_img03,
  default as no_img04,
  default as no_img05,
  default as no_img06,
  default as no_img07,
  default as no_img08,
  default as no_img09,
} from '@/assets/no_image.svg'
import {getPosterImageUrl} from '@/composables/common'

interface GalleryImage {
  large: string
  thumb: string
  alt?: string
}

// Fallback gallery images (01-09)
export const fallbackGalleryImages: GalleryImage[] = [
  {large: no_img01, thumb: no_img01},
  {large: no_img02, thumb: no_img02},
  {large: no_img03, thumb: no_img03},
  {large: no_img04, thumb: no_img04},
  {large: no_img05, thumb: no_img05},
  {large: no_img06, thumb: no_img06},
  {large: no_img07, thumb: no_img07},
  {large: no_img08, thumb: no_img08},
  {large: no_img09, thumb: no_img09},
]

// Build gallery images from itemDetail.images structure
export const buildGalleryImages = (
  images?: Array<{file_path?: string | null; postar_file_path?: string | null}>
): GalleryImage[] => {
  if (!Array.isArray(images) || images.length === 0) {
    return fallbackGalleryImages
  }

  const urls = images.map(img => getPosterImageUrl(img)).filter(u => !!u) as string[]

  const gallery = urls.map(url => ({large: url, thumb: url}))
  console.log('%c 🥢: gallery ', 'font-size:16px;background-color:#295577;color:white;', gallery)

  return gallery.length > 0 ? gallery : fallbackGalleryImages
}
