import {
  default as img01,
  default as img02,
  default as img03,
  default as img04,
  default as img05,
  default as img06,
  default as img07,
} from '@/assets/no_image.svg'
import type {GalleryImage} from '@/composables/_type'
import {getPosterImageUrl} from '@/composables/common'

// Fallback gallery images (01-09)
export const fallbackGalleryImages: GalleryImage[] = [
  {large: img01, thumb: img01, size: '500x500'},
  {large: img02, thumb: img02, size: '1200x1200'},
  {large: img03, thumb: img03, size: '1200x1200'},
  {large: img04, thumb: img04, size: '1200x1200'},
  {large: img05, thumb: img05, size: '1200x1200'},
  {large: img06, thumb: img06, size: '1200x1200'},
  {large: img07, thumb: img07, size: '1200x1200'},
]

// Build gallery images from itemDetail.images structure
export const buildGalleryImages = (
  images?: Array<{file_path?: string | null; postar_file_path?: string | null}>
): GalleryImage[] => {
  if (!Array.isArray(images) || images.length === 0) {
    return fallbackGalleryImages
  }

  const urls = images.map(img => getPosterImageUrl(img)).filter(u => !!u) as string[]

  const gallery = urls.map(url => ({large: url, thumb: url, size: '1200x1200'}))
  console.log('%c 🥢: gallery ', 'font-size:16px;background-color:#295577;color:white;', gallery)

  return gallery.length > 0 ? gallery : fallbackGalleryImages
}
