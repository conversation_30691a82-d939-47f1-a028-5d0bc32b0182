@charset "UTF-8";
/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *共通パーツ
 * *********************************************************************** */
/* 前へ・次へボタン
 * *========================================== */
#item-detail .back {
  width: 1280px;
  max-width: 100%;
  margin: 2rem auto 0;
  padding: 0 3rem;
}
@media screen and (max-width: 767px) {
  #item-detail .back {
    width: 100%;
    margin: 3vw 0 2vw;
    padding: 0 4vw;
  }
}
#item-detail .back .text {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  position: relative;
  width: auto;
  padding: 0.5rem 1.3rem;
  color: #427fae;
  font-size: 0.8rem;
  text-decoration: none;
  background-color: transparent;
  cursor: pointer;
}
@media screen and (max-width: 767px) {
  #item-detail .back .text {
    padding: 0 4vw;
    font-size: 3vw;
  }
}
#item-detail .back .text:before {
  content: '';
  position: absolute;
  left: 3px;
  top: calc(50% + 0px);
  width: 5px;
  height: 5px;
  border-right: 1px solid #333;
  border-bottom: 1px solid #333;
  -webkit-transform: translateY(-50%) rotate(135deg);
  transform: translateY(-50%) rotate(135deg);
}
@media screen and (max-width: 767px) {
  #item-detail .back .text:before {
    top: calc(50% + 0.3vw);
    width: 1.2vw;
    height: 1.2vw;
    border-right: 1px solid #ccc;
    border-bottom: 1px solid #ccc;
  }
}
#item-detail .back .text:hover {
  text-decoration: underline;
}
#item-detail .back .text:hover:before {
  opacity: 0.8;
}
#item-detail .container {
  padding: 0 1rem 100px;
}
@media screen and (max-width: 767px) {
  #item-detail .container {
    padding: 0 4vw 8vw;
  }
}

#main .item-name-wrap {
  margin: 0 auto;
  padding: 1rem 2rem;
}
@media screen and (max-width: 767px) {
  #main .item-name-wrap {
    margin: 4vw 0 4vw;
    padding: 0;
  }
}
#main .item-name-wrap .name {
  padding: 0;
  font-size: 1.2rem;
  font-weight: 600;
  line-height: 1.4;
}
@media screen and (max-width: 767px) {
  #main .item-name-wrap .name {
    display: block;
    padding: 0 0 2vw;
    font-size: 4.2vw;
    line-height: 1.5;
  }
}
#main .item-name-wrap .tag_status {
  display: inline-block;
  height: auto;
  margin: 0 0 0 10px;
  padding: 3px 10px 4px;
  font-size: 0.7rem;
  font-weight: 600;
  line-height: 1;
  border: 1px solid #333;
  border-radius: 2px;
  -webkit-transform: translateY(-2px);
  transform: translateY(-2px);
}
@media screen and (max-width: 767px) {
  #main .item-name-wrap .tag_status {
    margin: 1vw 0;
    padding: 0.7vw 3vw 1vw;
    font-size: 3vw;
  }
}
#main .tab-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 3px;
  width: 100%;
  margin: 0;
  padding: 0 0.2rem 0 2rem;
}
@media screen and (max-width: 767px) {
  #main .tab-wrap {
    margin: 0 0 4vw;
    padding: 0;
  }
}
#main .tab-wrap li {
  width: auto;
  margin: 0;
  padding: 1px 8px;
  border: 1px solid #333;
  font-size: 0.6rem;
  font-weight: 500;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
@media screen and (max-width: 767px) {
  #main .tab-wrap li {
    padding: 0.5vw 2vw;
    font-size: 2.2vw;
  }
}
#main .tab-wrap li.tab-main {
  color: #fff;
  background-color: #333;
}
#main .tab-wrap li.tab-sub {
  color: #333;
  border: 1px solid #333;
}
#main .tab-wrap li.tab-standard {
  color: #427fae;
  border: 1px solid #427fae;
}
#main #item-data {
  position: relative;
}
#main #item-data .item_d-main {
  width: 100%;
  margin-top: 40px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  padding: 0;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    margin: 2vw 0 0;
  }
}
#main #item-data .item_d-main .item_d-main-visual {
  width: calc(100% - 520px);
  padding: 0 2rem;
}
@media screen and (max-width: 1080px) {
  #main #item-data .item_d-main .item_d-main-visual {
    width: 50%;
    padding: 0 1rem;
  }
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-visual {
    width: 100%;
    padding: 0 0 4vw;
  }
}
#main #item-data .item_d-main .item_d-main-visual .slider_wrap {
  width: 100%;
}
#main #item-data .item_d-main .item_d-main-visual .slider_wrap ul.slider-nav {
  margin: 15px 0 0;
}
#main #item-data .item_d-main .item_d-main-visual .slider_wrap ul.slider-nav li {
  margin: 0 3px;
  overflow: hidden;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
#main #item-data .item_d-main .item_d-main-visual .slider_wrap ul.slider-nav button {
  display: block;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-visual .slider_wrap ul.slider-nav button {
    display: none !important;
  }
}
#main #item-data .item_d-main .item_d-main-visual .slider_wrap ul.slider-nav button:hover {
  display: block;
}
#main #item-data .item_d-main .item_d-main-txt {
  width: 520px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -ms-flex-line-pack: start;
  align-content: flex-start;
  padding: 0 2rem 0 1rem;
}
@media screen and (max-width: 1080px) {
  #main #item-data .item_d-main .item_d-main-txt {
    width: 50%;
    padding: 0 1rem;
  }
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt {
    width: 100%;
    padding: 0;
  }
}
#main #item-data .item_d-main .item_d-main-txt .bid-mode {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 100%;
  margin: 0;
  padding: 0.9rem 1rem;
  background-color: #000;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .bid-mode {
    padding: 3vw 2vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .bid-mode .mode-name {
  padding: 0;
  border-radius: 0;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .bid-mode .mode-name {
    font-size: 3vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .bid-mode .mode-name span {
  display: inline-block;
  margin: 0;
  color: #fff;
  font-size: 1rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .bid-mode .mode-name span {
    font-size: 3.5vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .bid-mode .update {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  position: relative;
  width: auto;
  height: 34px;
  margin: 0;
  padding: 0 1rem 0 1.9rem;
  color: #333;
  text-align: center;
  background-color: #fff;
  border: 1px solid #333;
  border-radius: 4px;
  cursor: pointer;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .bid-mode .update {
    width: 20vw;
    height: 10vw;
    padding: 0 3vw 0 8.7vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .bid-mode .update span {
  font-size: 0.8rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .bid-mode .update span {
    font-size: 3.2vw;
    font-weight: 600;
  }
}
#main #item-data .item_d-main .item_d-main-txt .bid-mode .update::after {
  content: '';
  display: inline-block;
  background: url('../img/common/icn_refresh_bk.svg') center 8px no-repeat;
  background-size: 14px auto;
  background-position: center calc(50% + 1px);
  width: 18px;
  height: 18px;
  position: absolute;
  top: calc(50% - 10px);
  left: 10px;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .bid-mode .update::after {
    background-size: 3.8vw auto;
    background-position: center;
    width: 5vw;
    height: 5vw;
    top: calc(50% - 2.4vw);
    left: 3.2vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .bid-mode .update:hover {
  opacity: 1;
  background-color: #f8f8f8;
}
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data {
  width: 100%;
  border: none;
}
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data dl.bid-price {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 0;
  margin: 0;
  padding: 1rem 0.3rem;
  background-color: #f0f8ff;
  background-color: #fff;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-data dl.bid-price {
    padding: 3vw 0 4vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data dl.bid-price dd {
  display: inline-block;
  margin: 0;
  padding: 5px 0;
  font-size: 0.8rem;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-data dl.bid-price dd {
    font-size: 3.5vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data dl.bid-price dd .label {
  -webkit-box-flex: 0;
  -ms-flex: 0 1 auto;
  flex: 0 1 auto;
  margin-right: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-data dl.bid-price dd .label {
    font-size: 3.5vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data dl.bid-price dd .value {
  -webkit-box-flex: 1;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  text-align: left;
  font-size: 1.2rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-data dl.bid-price dd .value {
    font-size: 4.5vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data dl.bid-price dd .value .unit {
  display: inline-block;
  margin-left: 1px;
  font-size: 0.8rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-data dl.bid-price dd .value .unit {
    margin-left: 0.5vw;
    font-size: 3vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data dl.bid-price dd.price-now {
  padding: 5px 0 0;
}
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data dl.bid-price dd.price-now .value {
  color: #e50a09;
  font-size: 1.6rem;
}
@media screen and (max-width: 767px) {
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .item_d-main-data
    dl.bid-price
    dd.price-now
    .value {
    font-size: 5.5vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data dl.bid-price dd.tax-include {
  padding: 0 0 5px;
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .item_d-main-data
  dl.bid-price
  dd.tax-include
  .label {
  font-size: 0.65rem;
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .item_d-main-data
  dl.bid-price
  dd.tax-include
  .label:before {
  content: '（';
  font-size: 0.65rem;
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .item_d-main-data
  dl.bid-price
  dd.tax-include
  .value {
  font-size: 0.85rem;
  font-weight: 400;
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .item_d-main-data
  dl.bid-price
  dd.tax-include
  .value:after {
  content: '）';
  font-size: 0.65rem;
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .item_d-main-data
  dl.bid-price
  dd.tax-include
  .value
  .unit {
  font-size: 0.65rem;
  font-weight: 500;
}
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data dl.bid-price dd.min {
  padding: 5px 0 0;
}
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data dl.bid-price dd.min .value {
  font-size: 1.6rem;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-data dl.bid-price dd.min .value {
    font-size: 5.5vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-status {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 5px 5px;
  margin: 0 0 1rem;
  padding: 0 0.3rem;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-status {
    margin: 0 0 6vw;
    padding: 0;
  }
}
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-status span {
  display: inline-block;
  height: auto;
  padding: 0.5rem 1rem;
  color: #fff;
  font-size: 0.8rem;
  font-weight: 600;
  line-height: 1.2;
  border-radius: 4px;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-status span {
    padding: 2vw 4vw;
    font-size: 3vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-status span.during {
  background-color: #427fae;
}
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-status span.extended {
  background-color: #e98181;
}
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-status span.top {
  background-color: #e50a09;
}
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-status span.reserve {
  color: #333;
  background-color: #fff;
  border: 1px solid #333;
}
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-status span.lose {
  color: #e50a09;
  background-color: #fff;
  border: 1px solid #e50a09;
}
#main #item-data .item_d-main .item_d-main-txt .status-panel {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  gap: 10px;
  width: 100%;
  margin: 0 0 1rem;
  padding: 0 1.5rem;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .status-panel {
    margin: 0;
    padding: 4vw 0.5vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .status-panel.sealed {
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end;
}
#main #item-data .item_d-main .item_d-main-txt .status-panel .status-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  gap: 10px;
}
#main #item-data .item_d-main .item_d-main-txt .status-panel .status-wrap .status {
  height: auto;
  padding: 3px 10px;
  color: #fff;
  font-size: 0.8rem;
  font-weight: 600;
  border-radius: 4px;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .status-panel .status-wrap .status {
    height: auto;
    font-size: 3.2vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .status-panel .status-wrap .status.top {
  background-color: #e98181;
  border: 1px solid #e98181;
}
#main #item-data .item_d-main .item_d-main-txt .status-panel .status-wrap .status.overbid {
  color: #e98181;
  border: 1px solid #e98181;
}
#main #item-data .item_d-main .item_d-main-txt .status-panel .update {
  position: relative;
  width: 55px;
  height: 55px;
  margin: 0 0 0 1rem;
  padding: 1.5rem 0 0;
  color: #333;
  text-align: center;
  background-color: #fff;
  border: 1px solid #333;
  border-radius: 30px;
  cursor: pointer;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .status-panel .update {
    width: 14vw;
    height: 14vw;
    margin: 0 0 0 4vw;
    padding: 6.8vw 0 0;
    border-radius: 10vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .status-panel .update span {
  font-size: 0.7rem;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .status-panel .update span {
    font-size: 2.7vw;
    font-weight: 600;
  }
}
#main #item-data .item_d-main .item_d-main-txt .status-panel .update::after {
  content: '';
  display: inline-block;
  background: url('../img/common/icn_refresh_bk.svg') center 8px no-repeat;
  background-size: 18px auto;
  background-position: center;
  width: 20px;
  height: 20px;
  position: absolute;
  top: 8px;
  left: calc(50% - 10px);
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .status-panel .update::after {
    background-wrap: nowrap;
    background-size: 4.5vw auto;
    background-position: center;
    width: 5vw;
    height: 5vw;
    top: 2vw;
    left: calc(50% - 2.5vw);
  }
}
#main #item-data .item_d-main .item_d-main-txt .status-panel .update:hover {
  opacity: 1;
  background-color: #f8f8f8;
}
#main #item-data .item_d-main .item_d-main-txt .place-bid {
  position: relative;
  width: 100%;
  margin: 1rem 0 0;
  padding: 1rem 1.5rem;
  background-color: #f0f0f0;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid {
    padding: 1rem;
    border-bottom: 1px solid #fff;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .bidded-price {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: baseline;
  -ms-flex-align: baseline;
  align-items: baseline;
  margin: 0.5rem 0 1rem;
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .bidded-price dt {
  font-size: 1rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .bidded-price dt {
    font-size: 3.5vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .bidded-price dd {
  text-align: right;
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .bidded-price dd span.price {
  font-size: 1.6rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .bidded-price dd span.price {
    font-size: 6vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .bidded-price dd span.unit {
  margin-left: 3px;
  font-size: 1rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .bidded-price dd span.unit {
    font-size: 3.5vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .bid_head {
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
@media screen and (max-width: 1080px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .bid_head {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .bid_head .ttl {
  margin: 0;
  padding: 0;
}
@media screen and (max-width: 1080px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .bid_head .ttl {
    width: 100%;
    margin: 0 0 5px;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .bid_head .ttl span {
  display: block;
  font-size: 1rem;
  font-weight: 700;
  line-height: 1.4;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .bid_head .ttl span {
    display: inline-block;
    font-size: 3.5vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .bid_head .price {
  font-size: 1rem;
  font-weight: 700;
  font-family: YakuHanJP, 'Noto Sans JP', Arial, sans-serif;
}
@media screen and (max-width: 1080px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .bid_head .price {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: baseline;
    -ms-flex-align: baseline;
    align-items: baseline;
    width: 100%;
  }
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .bid_head .price {
    font-size: 4vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .bid_head .price input {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  margin: 0 0.5rem 0 0;
  padding: 5px;
  font-size: 1.4rem;
  font-weight: 600;
  text-align: right;
  border: none;
  border-radius: 4px;
}
@media screen and (max-width: 1080px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .bid_head .price input {
    width: calc(100% - 2.5rem);
  }
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .bid_head .price input {
    height: 12vw;
    font-size: 4vw;
  }
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .bid_head
  .price
  input::-webkit-input-placeholder {
  color: #ddd;
  font-size: 1.4rem;
  font-weight: 600;
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .bid_head .price input::-moz-placeholder {
  color: #ddd;
  font-size: 1.4rem;
  font-weight: 600;
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .bid_head
  .price
  input:-ms-input-placeholder {
  color: #ddd;
  font-size: 1.4rem;
  font-weight: 600;
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .bid_head
  .price
  input::-ms-input-placeholder {
  color: #ddd;
  font-size: 1.4rem;
  font-weight: 600;
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .bid_head .price input::placeholder {
  color: #ddd;
  font-size: 1.4rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .place-bid
    .bid_head
    .price
    input::-webkit-input-placeholder {
    font-size: 5.5vw;
  }
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .place-bid
    .bid_head
    .price
    input::-moz-placeholder {
    font-size: 5.5vw;
  }
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .place-bid
    .bid_head
    .price
    input:-ms-input-placeholder {
    font-size: 5.5vw;
  }
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .place-bid
    .bid_head
    .price
    input::-ms-input-placeholder {
    font-size: 5.5vw;
  }
  #main #item-data .item_d-main .item_d-main-txt .place-bid .bid_head .price input::placeholder {
    font-size: 5.5vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .bid_head .price .unit {
  font-size: 1rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .bid_head .price .unit {
    font-size: 3.5vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .bid-unit-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 0.5rem;
  width: 100%;
  margin: 0.8rem 0 0.5rem;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .bid-unit-wrap {
    gap: 2vw;
    margin: 3vw 0;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .bid-unit-wrap li {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  padding: 0;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .bid-unit-wrap li {
    width: auto;
    padding: 0;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .bid-unit-wrap li button.bid-unit {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  width: 100%;
  margin: 0;
  padding: 0.2rem 0.6rem 0.2rem 0.2rem;
  font-size: 0.85rem;
  font-weight: 500;
  font-family: YakuHanJP, 'Noto Sans JP', Arial, sans-serif;
  background-color: #fff;
  border-radius: 4px;
  white-space: nowrap;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .bid-unit-wrap li button.bid-unit {
    padding: 0.5vw 1vw 0.7vw 0.5vw;
    font-size: 3.5vw;
    border-radius: 1vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .bid-unit-wrap li button.bid-unit span {
  display: inline-block;
  position: relative;
  width: 20px;
  height: 20px;
  margin: 2px 5px 2px 2px;
  padding: 0;
  color: #fff;
  line-height: 1;
  background-color: #427fae;
  border-radius: 20px;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .bid-unit-wrap li button.bid-unit span {
    width: 5.5vw;
    height: 5.5vw;
    margin: 0 0.5vw;
    padding: 0;
  }
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .bid-unit-wrap
  li
  button.bid-unit
  span::after {
  content: '+';
  position: absolute;
  top: 0;
  left: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 100%;
  height: 100%;
  color: #fff;
  font-size: 1rem;
  -webkit-transform: translateY(-1px);
  transform: translateY(-1px);
}
@media screen and (max-width: 767px) {
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .place-bid
    .bid-unit-wrap
    li
    button.bid-unit
    span::after {
    width: 5.5vw;
    height: 5.5vw;
    font-size: 4.4vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .note {
  width: 100%;
  font-size: 0.65rem;
  text-align: right;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .note {
    font-size: 2.5vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .btn-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 10px;
  margin: 1.5rem 0;
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .btn-wrap .btn {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 100%;
  height: 74px;
  margin: 0 auto;
  color: #fff;
  font-size: 1.2rem;
  font-weight: 600;
  background-color: #427fae;
  border-radius: 50px;
  line-height: 1.2;
}
@media screen and (max-width: 1080px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .btn-wrap .btn {
    height: 60px;
  }
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .btn-wrap .btn {
    height: 16vw;
    font-size: 4.5vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .btn-wrap .btn:hover {
  opacity: 0.8;
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .btn-wrap .btn .pct {
  width: 18px;
  height: auto;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .btn-wrap .btn .pct {
    width: 4.6vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .btn-wrap .btn .bid-text {
  position: relative;
  width: auto;
  display: inline-block;
  padding-left: 14px;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .btn-wrap .btn .bid-text {
    padding-left: 2vw;
    font-size: 4.2vw;
    font-weight: 600;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .btn-wrap .btn.cancel {
  height: 54px;
  color: #e98181;
  font-size: 1rem;
  background-color: #fff;
  border: 1px solid #e98181;
}
@media screen and (max-width: 1080px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .btn-wrap .btn.cancel {
    height: 54px;
  }
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .btn-wrap .btn.cancel {
    height: 12vw;
    font-size: 3.5vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .btn-wrap .btn.cancel:hover {
  color: #fff;
  background-color: #e98181;
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .btn-wrap .btn.cancel .bid-text {
  padding-left: 0;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .btn-wrap .btn.cancel .bid-text {
    font-size: 3.5vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .btn-wrap .btn.chat {
  width: 60%;
  height: 44px;
  margin: 1rem auto 0;
  background-color: #333;
}
@media screen and (max-width: 1080px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .btn-wrap .btn.chat {
    width: 100%;
  }
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .btn-wrap .btn.chat {
    width: 100%;
    height: 12vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .btn-wrap .btn.chat .pct {
  width: 20px;
  height: auto;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .btn-wrap .btn.chat .pct {
    width: 4.5vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .btn-wrap .btn.chat .text {
  display: inline-block;
  padding-left: 14px;
  font-size: 0.9rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .btn-wrap .btn.chat .text {
    padding-left: 2vw;
    font-size: 3.5vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .btn-wrap .view_comment {
  display: inline-block;
  margin: 0 auto 1rem;
  text-decoration: none;
  color: #427fae;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .btn-wrap .view_comment {
    font-size: 3.5vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .btn-wrap .view_comment:hover {
  text-decoration: underline;
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .current-status {
  width: 100%;
  padding: 0;
  text-align: center;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .current-status {
    width: 100%;
    background-color: #f0f0f0;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .period {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 10px;
  padding: 1rem 1rem;
  border-top: 1px solid #e1e1e1;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .period {
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start;
    padding: 4vw 2vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .period img {
  width: 16px;
  height: auto;
  margin: 0;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .period img {
    width: 3.8vw;
    -webkit-transform: translateY(1vw);
    transform: translateY(1vw);
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .period .text-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 10px;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .period .text-wrap {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start;
    gap: 0;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .period .text-wrap .end-l,
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .current-status
  .period
  .text-wrap
  .end-v {
  font-size: 0.8rem;
  font-weight: 600;
  font-family: YakuHanJP, 'Noto Sans JP', Arial, sans-serif;
}
@media screen and (max-width: 767px) {
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .place-bid
    .current-status
    .period
    .text-wrap
    .end-l,
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .place-bid
    .current-status
    .period
    .text-wrap
    .end-v {
    font-size: 3.8vw;
  }
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .current-status
  .period
  .text-wrap
  .end-l
  span,
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .current-status
  .period
  .text-wrap
  .end-v
  span {
  font-size: 0.8rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .place-bid
    .current-status
    .period
    .text-wrap
    .end-l
    span,
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .place-bid
    .current-status
    .period
    .text-wrap
    .end-v
    span {
    font-size: 3.5vw;
  }
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .current-status
  .period
  .text-wrap
  .end-l
  span.mark,
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .current-status
  .period
  .text-wrap
  .end-v
  span.mark {
  display: inline-block;
  margin-left: 10px;
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .current-status
  .period
  .text-wrap
  .end-l
  span
  + span,
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .current-status
  .period
  .text-wrap
  .end-v
  span
  + span {
  margin-left: 10px;
}
@media screen and (max-width: 767px) {
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .place-bid
    .current-status
    .period
    .text-wrap
    .end-l
    span
    + span,
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .place-bid
    .current-status
    .period
    .text-wrap
    .end-v
    span
    + span {
    margin-left: 2vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .end-date {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 10px;
  padding: 1rem 1rem;
  border-top: 1px solid #e1e1e1;
  border-bottom: 1px solid #e1e1e1;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .end-date {
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start;
    padding: 4vw 2vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .end-date img {
  width: 16px;
  height: auto;
  margin: 0;
  -webkit-transform: translateY(0px);
  transform: translateY(0px);
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .end-date img {
    width: 4vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .end-date .end-l,
#main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .end-date .end-v {
  font-size: 0.8rem;
  font-weight: 600;
  font-family: YakuHanJP, 'Noto Sans JP', Arial, sans-serif;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .end-date .end-l,
  #main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .end-date .end-v {
    font-size: 3.5vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .end-date .end-l span,
#main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .end-date .end-v span {
  font-size: 0.8rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .end-date .end-l span,
  #main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .end-date .end-v span {
    font-size: 3.5vw;
  }
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .current-status
  .end-date
  .end-l
  span
  + span,
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .current-status
  .end-date
  .end-v
  span
  + span {
  margin-left: 10px;
}
@media screen and (max-width: 767px) {
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .place-bid
    .current-status
    .end-date
    .end-l
    span
    + span,
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .place-bid
    .current-status
    .end-date
    .end-v
    span
    + span {
    margin-left: 2vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .other-info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  gap: 1.5rem;
  margin: 0 auto;
  padding: 1rem 1rem 0.3rem;
  font-size: 0.8rem;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .other-info {
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start;
    width: 100%;
    padding: 4vw 2vw 3vw;
    gap: 4vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .other-info .view img {
  width: 18px;
  height: auto;
  margin: 0 5px 0 0;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .other-info .view img {
    width: 4.8vw;
  }
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .current-status
  .other-info
  .favorite
  img {
  width: 18px;
  height: auto;
  margin: 0 5px 0 0;
}
@media screen and (max-width: 767px) {
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .place-bid
    .current-status
    .other-info
    .favorite
    img {
    width: 4vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .other-info .bid img {
  width: 14px;
  height: auto;
  margin: 0 5px 0 0;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .other-info .bid img {
    width: 3.6vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .other-info div {
  width: auto;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .other-info div span {
  font-size: 0.8rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .other-info div span {
    font-size: 3.5vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .com-item-box {
  margin: 1rem 0 1rem;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 100%;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .com-item-box {
    -webkit-box-ordinal-group: 6;
    -ms-flex-order: 5;
    order: 5;
  }
}
#main #item-data .item_d-main .item_d-main-txt .com-item-box #sns-share {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  min-height: 48px;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}
#main #item-data .item_d-main .item_d-main-txt .com-item-box #sns-share > p {
  width: auto;
  font-size: 0.65rem;
  font-weight: 500;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .com-item-box #sns-share > p {
    font-size: 2.5vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .com-item-box #sns-share > ul {
  width: auto;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-left: 5px;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .com-item-box #sns-share > ul {
    margin-left: 1vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .com-item-box #sns-share > ul > li {
  margin-left: 10px;
}
#main #item-data .item_d-main .item_d-main-txt .com-item-box #sns-share > ul > li img {
  width: 24px;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .com-item-box #sns-share > ul > li img {
    width: 5vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .com-item-box #sns-share > ul > li img.x {
  width: 22px;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .com-item-box #sns-share > ul > li img.x {
    width: 4.7vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .com-item-box #sns-share > ul > li img.instagram {
  width: 23px;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .com-item-box #sns-share > ul > li img.instagram {
    width: 4.7vw;
  }
}
#main .item-note {
  width: 100%;
  max-width: 100%;
  margin: 60px auto 0;
  padding: 0 2rem;
}
@media screen and (max-width: 1080px) {
  #main .item-note {
    padding: 0 2rem;
  }
}
@media screen and (max-width: 767px) {
  #main .item-note {
    margin: 40px auto 0;
    padding: 0;
  }
}
#main .item-note h2 {
  margin: 0;
  padding: 0 0 0.8rem;
  font-size: 1.3rem;
  font-weight: 700;
  border-bottom: 1px solid #f8f8f8;
}
@media screen and (max-width: 767px) {
  #main .item-note h2 {
    height: auto;
    font-size: 4vw;
  }
}
#main .item-note .contents-wrap {
  padding: 0;
}
#main .item-note .contents-wrap h3 {
  width: 100%;
  margin: 0.8rem 0 1rem;
  padding: 0.8rem 0.8rem;
  font-size: 1.1rem;
  font-weight: 600;
  text-align: left;
  background-color: #f8f8f8;
}
@media screen and (max-width: 767px) {
  #main .item-note .contents-wrap h3 {
    font-size: 3.5vw;
  }
}
#main .item-note .contents-wrap p {
  margin: 1.2rem 0 0;
  font-size: 0.9rem;
}
@media screen and (max-width: 767px) {
  #main .item-note .contents-wrap p {
    font-size: 3.2vw;
  }
}
#main .item-note .contents-wrap ul li {
  padding: 0 1rem;
  text-indent: -0.8rem;
  font-size: 0.9rem;
}
@media screen and (max-width: 767px) {
  #main .item-note .contents-wrap ul li {
    padding: 0 4vw;
    font-size: 3.2vw;
    text-indent: -2.8vw;
  }
}
#main .item-note .contents-wrap table.spec {
  width: 100%;
  margin: 2rem 0 0;
}
#main .item-note .contents-wrap table.spec tr th,
#main .item-note .contents-wrap table.spec tr td {
  padding: 1rem 1rem;
  font-size: 0.9rem;
  background-color: #f8f8f8;
  border: 1px solid #fff;
  line-height: 1.4;
}
@media screen and (max-width: 767px) {
  #main .item-note .contents-wrap table.spec tr th,
  #main .item-note .contents-wrap table.spec tr td {
    padding: 4vw;
    font-size: 3.2vw;
  }
}
#main .item-note .contents-wrap table.spec tr th {
  width: 200px;
  font-weight: 700;
  white-space: nowrap;
  text-align: center;
  background-color: #ededed;
}
@media screen and (max-width: 767px) {
  #main .item-note .contents-wrap table.spec tr th {
    vertical-align: middle;
    width: 32%;
  }
}

/*---------------------------- */
.item_d-bid-history {
  width: 100%;
  padding: 60px 0 0;
}
@media screen and (max-width: 767px) {
  .item_d-bid-history {
    margin: 2vw 0 0;
    padding: 0;
  }
}
.item_d-bid-history p {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.5rem;
}
@media screen and (max-width: 767px) {
  .item_d-bid-history p {
    margin: 0 0 1vw;
    font-size: 3.5vw;
  }
}
.item_d-bid-history table {
  width: 100%;
  border: none;
  border-top: 1px solid #f8f8f8;
}
.item_d-bid-history table tr {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  border-bottom: 1px solid #f8f8f8;
  border-right: 1px solid #f8f8f8;
  border-left: 1px solid #f8f8f8;
}
.item_d-bid-history table th,
.item_d-bid-history table td {
  padding: 10px 10px;
  font-size: 0.7rem;
}
@media screen and (max-width: 767px) {
  .item_d-bid-history table th,
  .item_d-bid-history table td {
    padding: 2.5vw;
    font-size: 3vw;
  }
}
.item_d-bid-history table th.time,
.item_d-bid-history table td.time {
  width: 30%;
}
@media screen and (max-width: 767px) {
  .item_d-bid-history table th.time,
  .item_d-bid-history table td.time {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    width: 37%;
  }
}
.item_d-bid-history table th.time span,
.item_d-bid-history table td.time span {
  display: inline-block;
}
.item_d-bid-history table th.amount,
.item_d-bid-history table td.amount {
  width: 40%;
}
@media screen and (max-width: 767px) {
  .item_d-bid-history table th.amount,
  .item_d-bid-history table td.amount {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    width: 33%;
  }
}
.item_d-bid-history table th.bidder,
.item_d-bid-history table td.bidder {
  width: 30%;
  -webkit-box-flex: 1;
  -ms-flex: 1 1;
  flex: 1 1;
}
@media screen and (max-width: 767px) {
  .item_d-bid-history table th.bidder,
  .item_d-bid-history table td.bidder {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    width: 30%;
  }
}
.item_d-bid-history table th.bidder .highest,
.item_d-bid-history table td.bidder .highest {
  display: inline;
  margin: 0;
  padding: 2px 10px;
  color: #6f6f6f;
  font-size: 0.6rem;
  font-weight: 600;
  white-space: nowrap;
  background-color: #f8f8f8;
  border-radius: 2px;
}
@media screen and (max-width: 767px) {
  .item_d-bid-history table th.bidder .highest,
  .item_d-bid-history table td.bidder .highest {
    width: -webkit-fit-content;
    width: -moz-fit-content;
    width: fit-content;
    padding: 1vw 3vw;
    font-size: 2.5vw;
  }
}
.item_d-bid-history table th {
  font-weight: 600;
  background-color: #f8f8f8;
}
.item_d-bid-history dl {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin: 0.5rem 0;
}
@media screen and (max-width: 767px) {
  .item_d-bid-history dl {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    margin: 2vw 0;
  }
}
.item_d-bid-history dl dt,
.item_d-bid-history dl dd {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  margin: 0 0 5px;
  padding: 0.5rem;
  font-size: 0.8rem;
  font-weight: 400;
  line-height: 1.2;
  background-color: #f5f5f5;
}
@media screen and (max-width: 767px) {
  .item_d-bid-history dl dt,
  .item_d-bid-history dl dd {
    height: 5.7vw;
    font-size: 3.2vw;
  }
}
.item_d-bid-history dl dt .max-bid,
.item_d-bid-history dl dd .max-bid {
  display: inline;
  margin: 0 0 0 0.5rem;
  font-size: 0.8rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  .item_d-bid-history dl dt .max-bid,
  .item_d-bid-history dl dd .max-bid {
    font-size: 3.5vw;
  }
}
.item_d-bid-history dl dt {
  width: calc(100% - 170px);
}
@media screen and (max-width: 767px) {
  .item_d-bid-history dl dt {
    width: 100%;
    margin: 0;
  }
}
.item_d-bid-history dl dd {
  width: 170px;
}
@media screen and (max-width: 767px) {
  .item_d-bid-history dl dd {
    width: 100%;
  }
}

/***********************************************************************
 * *
 * *---------------------------------------------------------------------- */
#main .modal-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  text-align: center;
  background: rgba(0, 0, 0, 0.7);
  padding: 40px 20px;
  overflow: auto;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  z-index: 100;
}
@media screen and (max-width: 767px) {
  #main .modal-container {
    padding: 10vw 4vw;
  }
}
#main .modal-container:before {
  content: '';
  display: inline-block;
  vertical-align: middle;
  height: 100%;
}
#main .modal-container.active {
  opacity: 1;
  visibility: visible;
}
#main .modal-container .modal-body {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  max-width: calc(100% - 2rem);
  width: 700px;
  margin: 0 auto;
}
@media screen and (max-width: 767px) {
  #main .modal-container .modal-body {
    max-width: calc(100% - 4vw);
  }
}
#main .modal-container .modal-body .modal-close {
  position: absolute;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  top: -45px;
  right: 0;
  width: 40px;
  height: 40px;
  cursor: pointer;
  z-index: 120;
  border-radius: 50px;
}
@media screen and (max-width: 767px) {
  #main .modal-container .modal-body .modal-close {
    top: -10vw;
    right: 0;
    width: 10vw;
    height: 10vw;
  }
}
#main .modal-container .modal-body .modal-close:after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 50%;
  height: 2px;
  background: #fff;
  -webkit-transform: translate(-50%, -50%) rotate(45deg);
  transform: translate(-50%, -50%) rotate(45deg);
}
#main .modal-container .modal-body .modal-close:before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 50%;
  height: 2px;
  background: #fff;
  -webkit-transform: translate(-50%, -50%) rotate(-45deg);
  transform: translate(-50%, -50%) rotate(-45deg);
}
#main .modal-container .modal-body .modal-close:hover {
  background-color: rgba(0, 0, 0, 0.1);
}
@media screen and (max-width: 767px) {
  #main .modal-container .modal-body .modal-close:hover {
    background-color: transparent;
  }
}
#main .modal-container .modal-body .modal-content {
  position: relative;
  padding: 2rem;
  background-color: #fff;
  border-radius: 8px;
  z-index: 110;
}
@media screen and (max-width: 767px) {
  #main .modal-container .modal-body .modal-content {
    padding: 4vw;
  }
}
#main .modal-container .modal-body .modal-content .item-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  margin: 0;
  padding: 0;
}
#main .modal-container .modal-body .modal-content .item-wrap .item-panel {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  width: 100%;
  margin: 0;
  padding: 0 0 1.5rem;
  border-bottom: 1px solid #ccc;
}
@media screen and (max-width: 767px) {
  #main .modal-container .modal-body .modal-content .item-wrap .item-panel {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    padding: 0 0 2vw;
  }
}
#main .modal-container .modal-body .modal-content .item-wrap .item-panel figure {
  width: 150px;
}
@media screen and (max-width: 767px) {
  #main .modal-container .modal-body .modal-content .item-wrap .item-panel figure {
    width: 100%;
    height: auto;
    margin: 0 0 4vw;
  }
}
#main .modal-container .modal-body .modal-content .item-wrap .item-panel figure .wrap_pct {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  position: relative;
  width: 100%;
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
}
#main .modal-container .modal-body .modal-content .item-wrap .item-panel figure .wrap_pct img {
  max-width: 100%;
  max-height: 100%;
  -o-object-fit: contain;
  object-fit: contain;
  -o-object-position: center;
  object-position: center;
}
#main .modal-container .modal-body .modal-content .item-wrap .item-panel figure .wrap_pct .status {
  position: absolute;
  bottom: 10px;
  left: 12px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  height: auto;
}
#main
  .modal-container
  .modal-body
  .modal-content
  .item-wrap
  .item-panel
  figure
  .wrap_pct
  .status
  > p {
  display: inline-block;
  width: auto;
  max-width: 110px;
  margin: 2px 0;
  padding: 2px 12px;
  font-size: 0.8rem;
  font-weight: 700;
  text-align: center;
  border-radius: 20px;
}
#main
  .modal-container
  .modal-body
  .modal-content
  .item-wrap
  .item-panel
  figure
  .wrap_pct
  .status
  .status_top {
  color: #fff;
  background-color: #ff0000;
}
#main
  .modal-container
  .modal-body
  .modal-content
  .item-wrap
  .item-panel
  figure
  .wrap_pct
  .status
  .status_overbid {
  color: #ff0000;
  background-color: #d3d1d0;
}
#main
  .modal-container
  .modal-body
  .modal-content
  .item-wrap
  .item-panel
  figure
  .wrap_pct
  .status_soldout.active {
  display: block;
  position: absolute;
  top: 26%;
  left: 10%;
  width: 80%;
}
#main
  .modal-container
  .modal-body
  .modal-content
  .item-wrap
  .item-panel
  figure
  .wrap_pct
  .status_soldout.active
  img {
  width: 100%;
}
#main
  .modal-container
  .modal-body
  .modal-content
  .item-wrap
  .item-panel
  figure
  .wrap_pct
  .status_soldout {
  display: none;
}
#main .modal-container .modal-body .modal-content .item-wrap .item-panel .product-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  width: calc(100% - 150px);
  padding: 0 0 0 1rem;
}
@media screen and (max-width: 767px) {
  #main .modal-container .modal-body .modal-content .item-wrap .item-panel .product-wrap {
    width: 100%;
    padding: 0;
  }
}
#main
  .modal-container
  .modal-body
  .modal-content
  .item-wrap
  .item-panel
  .product-wrap
  .modal-item-name {
  position: relative;
  width: 100%;
  margin: 0;
  padding: 0 0 1rem;
  font-size: 0.9rem;
  font-weight: 500;
  line-height: 1.5;
  border-bottom: 1px solid #ccc;
}
@media screen and (max-width: 767px) {
  #main
    .modal-container
    .modal-body
    .modal-content
    .item-wrap
    .item-panel
    .product-wrap
    .modal-item-name {
    font-size: 3.5vw;
    line-height: 1.5;
    padding: 0 0 4vw;
  }
}
#main
  .modal-container
  .modal-body
  .modal-content
  .item-wrap
  .item-panel
  .product-wrap
  .modal-item-name
  .tag_status {
  display: inline-block;
  height: auto;
  margin: 0 10px;
  padding: 3px 10px 4px;
  font-size: 0.7rem;
  font-weight: 600;
  line-height: 1;
  border: 1px solid #333;
  border-radius: 2px;
  -webkit-transform: translateY(-2px);
  transform: translateY(-2px);
}
@media screen and (max-width: 767px) {
  #main
    .modal-container
    .modal-body
    .modal-content
    .item-wrap
    .item-panel
    .product-wrap
    .modal-item-name
    .tag_status {
    margin: 0 2vw;
    padding: 0.7vw 3vw 1vw;
  }
}
#main
  .modal-container
  .modal-body
  .modal-content
  .item-wrap
  .item-panel
  .product-wrap
  .modal-item-name
  .tag_status
  p {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 6px 14px;
  font-size: 0.75rem;
  font-weight: 700;
  line-height: 1;
  border-radius: 4px;
  white-space: nowrap;
}
@media screen and (max-width: 767px) {
  #main
    .modal-container
    .modal-body
    .modal-content
    .item-wrap
    .item-panel
    .product-wrap
    .modal-item-name
    .tag_status
    p {
    padding: 1.5vw 4.2vw 1.7vw;
    font-size: 3vw;
    border-radius: 1vw;
  }
}
#main
  .modal-container
  .modal-body
  .modal-content
  .item-wrap
  .item-panel
  .product-wrap
  .modal-item-name
  .tag_status
  .free-shipping {
  color: #fff;
  background-color: #333;
}
#main
  .modal-container
  .modal-body
  .modal-content
  .item-wrap
  .item-panel
  .product-wrap
  .modal-item-name
  .tag_status
  .campaign {
  color: #fff;
  background-color: #427fae;
}
#main .modal-container .modal-body .modal-content .item-wrap .item-panel .product-wrap .sell {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
}
#main
  .modal-container
  .modal-body
  .modal-content
  .item-wrap
  .item-panel
  .product-wrap
  .sell
  .price {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-align: baseline;
  -ms-flex-align: baseline;
  align-items: baseline;
  margin: 0.5rem 0;
  padding: 0;
}
@media screen and (max-width: 767px) {
  #main
    .modal-container
    .modal-body
    .modal-content
    .item-wrap
    .item-panel
    .product-wrap
    .sell
    .price {
    margin: 2vw 0 2vw;
    padding: 0;
    border-bottom: 1px solid #fff;
  }
}
#main
  .modal-container
  .modal-body
  .modal-content
  .item-wrap
  .item-panel
  .product-wrap
  .sell
  .price
  .label {
  display: inline-block;
  margin-right: 5px;
  font-size: 0.9rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #main
    .modal-container
    .modal-body
    .modal-content
    .item-wrap
    .item-panel
    .product-wrap
    .sell
    .price
    .label {
    margin-right: 1vw;
    font-size: 3.2vw;
  }
}
#main
  .modal-container
  .modal-body
  .modal-content
  .item-wrap
  .item-panel
  .product-wrap
  .sell
  .price
  .price-v {
  color: #e50a09;
  font-size: 1.6rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #main
    .modal-container
    .modal-body
    .modal-content
    .item-wrap
    .item-panel
    .product-wrap
    .sell
    .price
    .price-v {
    font-size: 5.8vw;
  }
}
#main
  .modal-container
  .modal-body
  .modal-content
  .item-wrap
  .item-panel
  .product-wrap
  .sell
  .price
  .price-v
  .unit {
  display: inline-block;
  color: #e50a09;
  font-size: 0.7rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #main
    .modal-container
    .modal-body
    .modal-content
    .item-wrap
    .item-panel
    .product-wrap
    .sell
    .price
    .price-v
    .unit {
    margin-left: 0.5vw;
    font-size: 2.6vw;
  }
}
#main
  .modal-container
  .modal-body
  .modal-content
  .item-wrap
  .item-panel
  .product-wrap
  .sell
  .sch-to-end {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 10px;
  width: 100%;
  padding: 0.5rem 0 0;
  background-color: transparent;
}
@media screen and (max-width: 767px) {
  #main
    .modal-container
    .modal-body
    .modal-content
    .item-wrap
    .item-panel
    .product-wrap
    .sell
    .sch-to-end {
    gap: 2vw;
    margin: 2vw 0;
  }
}
#main
  .modal-container
  .modal-body
  .modal-content
  .item-wrap
  .item-panel
  .product-wrap
  .sell
  .sch-to-end
  .period {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 10px;
  padding: 0;
}
@media screen and (max-width: 767px) {
  #main
    .modal-container
    .modal-body
    .modal-content
    .item-wrap
    .item-panel
    .product-wrap
    .sell
    .sch-to-end
    .period {
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start;
    padding: 0;
  }
}
#main
  .modal-container
  .modal-body
  .modal-content
  .item-wrap
  .item-panel
  .product-wrap
  .sell
  .sch-to-end
  .period
  img {
  width: 14px;
  height: auto;
  margin: 0;
}
@media screen and (max-width: 767px) {
  #main
    .modal-container
    .modal-body
    .modal-content
    .item-wrap
    .item-panel
    .product-wrap
    .sell
    .sch-to-end
    .period
    img {
    width: 3.5vw;
    -webkit-transform: translateY(0.7vw);
    transform: translateY(0.7vw);
  }
}
#main
  .modal-container
  .modal-body
  .modal-content
  .item-wrap
  .item-panel
  .product-wrap
  .sell
  .sch-to-end
  .period
  .text-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 10px;
}
@media screen and (max-width: 767px) {
  #main
    .modal-container
    .modal-body
    .modal-content
    .item-wrap
    .item-panel
    .product-wrap
    .sell
    .sch-to-end
    .period
    .text-wrap {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start;
    gap: 0;
  }
}
#main
  .modal-container
  .modal-body
  .modal-content
  .item-wrap
  .item-panel
  .product-wrap
  .sell
  .sch-to-end
  .period
  .text-wrap
  .end-l,
#main
  .modal-container
  .modal-body
  .modal-content
  .item-wrap
  .item-panel
  .product-wrap
  .sell
  .sch-to-end
  .period
  .text-wrap
  .end-v {
  font-size: 0.8rem;
  font-weight: 600;
  font-family: YakuHanJP, 'Noto Sans JP', Arial, sans-serif;
}
@media screen and (max-width: 767px) {
  #main
    .modal-container
    .modal-body
    .modal-content
    .item-wrap
    .item-panel
    .product-wrap
    .sell
    .sch-to-end
    .period
    .text-wrap
    .end-l,
  #main
    .modal-container
    .modal-body
    .modal-content
    .item-wrap
    .item-panel
    .product-wrap
    .sell
    .sch-to-end
    .period
    .text-wrap
    .end-v {
    font-size: 3.2vw;
  }
}
#main
  .modal-container
  .modal-body
  .modal-content
  .item-wrap
  .item-panel
  .product-wrap
  .sell
  .sch-to-end
  .period
  .text-wrap
  .end-l
  span,
#main
  .modal-container
  .modal-body
  .modal-content
  .item-wrap
  .item-panel
  .product-wrap
  .sell
  .sch-to-end
  .period
  .text-wrap
  .end-v
  span {
  font-size: 0.8rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #main
    .modal-container
    .modal-body
    .modal-content
    .item-wrap
    .item-panel
    .product-wrap
    .sell
    .sch-to-end
    .period
    .text-wrap
    .end-l
    span,
  #main
    .modal-container
    .modal-body
    .modal-content
    .item-wrap
    .item-panel
    .product-wrap
    .sell
    .sch-to-end
    .period
    .text-wrap
    .end-v
    span {
    font-size: 3.2vw;
  }
}
#main
  .modal-container
  .modal-body
  .modal-content
  .item-wrap
  .item-panel
  .product-wrap
  .sell
  .sch-to-end
  .period
  .text-wrap
  .end-l
  span.mark,
#main
  .modal-container
  .modal-body
  .modal-content
  .item-wrap
  .item-panel
  .product-wrap
  .sell
  .sch-to-end
  .period
  .text-wrap
  .end-v
  span.mark {
  display: inline-block;
  margin-left: 10px;
}
#main
  .modal-container
  .modal-body
  .modal-content
  .item-wrap
  .item-panel
  .product-wrap
  .sell
  .sch-to-end
  .period
  .text-wrap
  .end-l
  span
  + span,
#main
  .modal-container
  .modal-body
  .modal-content
  .item-wrap
  .item-panel
  .product-wrap
  .sell
  .sch-to-end
  .period
  .text-wrap
  .end-v
  span
  + span {
  margin-left: 10px;
}
@media screen and (max-width: 767px) {
  #main
    .modal-container
    .modal-body
    .modal-content
    .item-wrap
    .item-panel
    .product-wrap
    .sell
    .sch-to-end
    .period
    .text-wrap
    .end-l
    span
    + span,
  #main
    .modal-container
    .modal-body
    .modal-content
    .item-wrap
    .item-panel
    .product-wrap
    .sell
    .sch-to-end
    .period
    .text-wrap
    .end-v
    span
    + span {
    margin-left: 2vw;
  }
}
#main
  .modal-container
  .modal-body
  .modal-content
  .item-wrap
  .item-panel
  .product-wrap
  .sell
  .sch-to-end
  .b-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  gap: 0 40px;
}
@media screen and (max-width: 767px) {
  #main
    .modal-container
    .modal-body
    .modal-content
    .item-wrap
    .item-panel
    .product-wrap
    .sell
    .sch-to-end
    .b-wrap {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    gap: 2vw 0;
  }
}
#main
  .modal-container
  .modal-body
  .modal-content
  .item-wrap
  .item-panel
  .product-wrap
  .sell
  .sch-to-end
  .b-wrap
  .end-date {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  -webkit-box-align: baseline;
  -ms-flex-align: baseline;
  align-items: baseline;
  gap: 10px;
  width: auto;
  margin: 0;
  padding: 0;
  border: none;
}
@media screen and (max-width: 767px) {
  #main
    .modal-container
    .modal-body
    .modal-content
    .item-wrap
    .item-panel
    .product-wrap
    .sell
    .sch-to-end
    .b-wrap
    .end-date {
    gap: 2vw;
    width: 100%;
  }
}
#main
  .modal-container
  .modal-body
  .modal-content
  .item-wrap
  .item-panel
  .product-wrap
  .sell
  .sch-to-end
  .b-wrap
  .end-date
  img {
  width: 14px;
  height: auto;
  margin: 0;
  -webkit-transform: translateY(2px);
  transform: translateY(2px);
}
@media screen and (max-width: 767px) {
  #main
    .modal-container
    .modal-body
    .modal-content
    .item-wrap
    .item-panel
    .product-wrap
    .sell
    .sch-to-end
    .b-wrap
    .end-date
    img {
    width: 3.5vw;
    -webkit-transform: translateY(0.6vw);
    transform: translateY(0.6vw);
  }
}
#main
  .modal-container
  .modal-body
  .modal-content
  .item-wrap
  .item-panel
  .product-wrap
  .sell
  .sch-to-end
  .b-wrap
  .end-date
  .end-l,
#main
  .modal-container
  .modal-body
  .modal-content
  .item-wrap
  .item-panel
  .product-wrap
  .sell
  .sch-to-end
  .b-wrap
  .end-date
  .end-v {
  font-size: 0.8rem;
  font-weight: 600;
  font-family: YakuHanJP, 'Noto Sans JP', Arial, sans-serif;
}
@media screen and (max-width: 767px) {
  #main
    .modal-container
    .modal-body
    .modal-content
    .item-wrap
    .item-panel
    .product-wrap
    .sell
    .sch-to-end
    .b-wrap
    .end-date
    .end-l,
  #main
    .modal-container
    .modal-body
    .modal-content
    .item-wrap
    .item-panel
    .product-wrap
    .sell
    .sch-to-end
    .b-wrap
    .end-date
    .end-v {
    font-size: 3.2vw;
  }
}
#main
  .modal-container
  .modal-body
  .modal-content
  .item-wrap
  .item-panel
  .product-wrap
  .sell
  .sch-to-end
  .b-wrap
  .end-date
  .end-l
  span,
#main
  .modal-container
  .modal-body
  .modal-content
  .item-wrap
  .item-panel
  .product-wrap
  .sell
  .sch-to-end
  .b-wrap
  .end-date
  .end-v
  span {
  font-size: 0.8rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #main
    .modal-container
    .modal-body
    .modal-content
    .item-wrap
    .item-panel
    .product-wrap
    .sell
    .sch-to-end
    .b-wrap
    .end-date
    .end-l
    span,
  #main
    .modal-container
    .modal-body
    .modal-content
    .item-wrap
    .item-panel
    .product-wrap
    .sell
    .sch-to-end
    .b-wrap
    .end-date
    .end-v
    span {
    font-size: 3.2vw;
  }
}
#main
  .modal-container
  .modal-body
  .modal-content
  .item-wrap
  .item-panel
  .product-wrap
  .sell
  .sch-to-end
  .b-wrap
  .end-date
  .end-l
  span.mark,
#main
  .modal-container
  .modal-body
  .modal-content
  .item-wrap
  .item-panel
  .product-wrap
  .sell
  .sch-to-end
  .b-wrap
  .end-date
  .end-v
  span.mark {
  display: inline-block;
  margin-left: 10px;
}
#main
  .modal-container
  .modal-body
  .modal-content
  .item-wrap
  .item-panel
  .product-wrap
  .sell
  .sch-to-end
  .b-wrap
  .end-date
  .end-l
  span
  + span,
#main
  .modal-container
  .modal-body
  .modal-content
  .item-wrap
  .item-panel
  .product-wrap
  .sell
  .sch-to-end
  .b-wrap
  .end-date
  .end-v
  span
  + span {
  margin-left: 10px;
}
@media screen and (max-width: 767px) {
  #main
    .modal-container
    .modal-body
    .modal-content
    .item-wrap
    .item-panel
    .product-wrap
    .sell
    .sch-to-end
    .b-wrap
    .end-date
    .end-l
    span
    + span,
  #main
    .modal-container
    .modal-body
    .modal-content
    .item-wrap
    .item-panel
    .product-wrap
    .sell
    .sch-to-end
    .b-wrap
    .end-date
    .end-v
    span
    + span {
    margin-left: 2vw;
  }
}
#main
  .modal-container
  .modal-body
  .modal-content
  .item-wrap
  .item-panel
  .product-wrap
  .sell
  .sch-to-end
  .b-wrap
  .other-info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: flex-end;
  gap: 1.5rem;
  width: 50%;
  margin: 0;
  padding: 0;
  font-size: 0.8rem;
}
@media screen and (max-width: 767px) {
  #main
    .modal-container
    .modal-body
    .modal-content
    .item-wrap
    .item-panel
    .product-wrap
    .sell
    .sch-to-end
    .b-wrap
    .other-info {
    gap: 4vw;
    width: 100%;
  }
}
#main
  .modal-container
  .modal-body
  .modal-content
  .item-wrap
  .item-panel
  .product-wrap
  .sell
  .sch-to-end
  .b-wrap
  .other-info
  .view
  img {
  width: 16px;
  height: auto;
  margin: 0 5px 0 0;
}
@media screen and (max-width: 767px) {
  #main
    .modal-container
    .modal-body
    .modal-content
    .item-wrap
    .item-panel
    .product-wrap
    .sell
    .sch-to-end
    .b-wrap
    .other-info
    .view
    img {
    width: 4.2vw;
  }
}
#main
  .modal-container
  .modal-body
  .modal-content
  .item-wrap
  .item-panel
  .product-wrap
  .sell
  .sch-to-end
  .b-wrap
  .other-info
  .favorite
  img {
  width: 14px;
  height: auto;
  margin: 0 5px 0 0;
}
@media screen and (max-width: 767px) {
  #main
    .modal-container
    .modal-body
    .modal-content
    .item-wrap
    .item-panel
    .product-wrap
    .sell
    .sch-to-end
    .b-wrap
    .other-info
    .favorite
    img {
    width: 3.2vw;
  }
}
#main
  .modal-container
  .modal-body
  .modal-content
  .item-wrap
  .item-panel
  .product-wrap
  .sell
  .sch-to-end
  .b-wrap
  .other-info
  .bid
  img {
  width: 12px;
  height: auto;
  margin: 0 5px 0 0;
}
@media screen and (max-width: 767px) {
  #main
    .modal-container
    .modal-body
    .modal-content
    .item-wrap
    .item-panel
    .product-wrap
    .sell
    .sch-to-end
    .b-wrap
    .other-info
    .bid
    img {
    width: 3.2vw;
  }
}
#main
  .modal-container
  .modal-body
  .modal-content
  .item-wrap
  .item-panel
  .product-wrap
  .sell
  .sch-to-end
  .b-wrap
  .other-info
  div {
  width: auto;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
#main
  .modal-container
  .modal-body
  .modal-content
  .item-wrap
  .item-panel
  .product-wrap
  .sell
  .sch-to-end
  .b-wrap
  .other-info
  div
  span {
  font-size: 0.8rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #main
    .modal-container
    .modal-body
    .modal-content
    .item-wrap
    .item-panel
    .product-wrap
    .sell
    .sch-to-end
    .b-wrap
    .other-info
    div
    span {
    font-size: 3.5vw;
  }
}
#main .modal-container .modal-body .modal-content .note-bid {
  width: 100%;
  margin: 1.5rem 0 0;
  padding: 0;
  font-size: 0.7rem;
  text-align: center;
}
@media screen and (max-width: 767px) {
  #main .modal-container .modal-body .modal-content .note-bid {
    margin: 4vw 0 0;
    font-size: 2.7vw;
  }
}
#main .modal-container .modal-body .modal-content .button-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 2rem;
  padding: 1rem 0 0;
}
@media screen and (max-width: 767px) {
  #main .modal-container .modal-body .modal-content .button-wrap {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    gap: 4vw;
    padding: 4vw 0 4vw;
  }
}
#main .modal-container .modal-body .modal-content .button-wrap .btn.bid-cancel {
  width: auto;
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
  color: #427fae;
  background-color: transparent;
}
@media screen and (max-width: 767px) {
  #main .modal-container .modal-body .modal-content .button-wrap .btn.bid-cancel {
    -webkit-box-ordinal-group: 3;
    -ms-flex-order: 2;
    order: 2;
    width: 100%;
  }
}
#main .modal-container .modal-body .modal-content .button-wrap .btn.bid-cancel span {
  color: #427fae;
  font-weight: 500;
}
@media screen and (max-width: 767px) {
  #main .modal-container .modal-body .modal-content .button-wrap .btn.bid-cancel span {
    font-size: 3.5vw;
  }
}
#main .modal-container .modal-body .modal-content .button-wrap .btn.bid-cancel:hover {
  text-decoration: underline;
}
#main .modal-container .modal-body .modal-content .button-wrap .btn.confirm {
  width: 280px;
  max-width: calc(100% - 2rem);
  height: 56px;
  margin: 0;
  padding-left: 30px;
  background-color: #427fae;
  border-radius: 50px;
}
@media screen and (max-width: 767px) {
  #main .modal-container .modal-body .modal-content .button-wrap .btn.confirm {
    -webkit-box-ordinal-group: 2;
    -ms-flex-order: 1;
    order: 1;
    width: 100%;
    height: 14vw;
    max-width: 100%;
    border-radius: 20vw;
  }
}
#main .modal-container .modal-body .modal-content .button-wrap .btn.confirm:hover {
  opacity: 0.8;
}
#main .modal-container .modal-body .modal-content .button-wrap .btn.confirm span {
  position: relative;
  display: inline-block;
  color: #fff;
  font-size: 1rem;
  font-weight: 500;
}
@media screen and (max-width: 767px) {
  #main .modal-container .modal-body .modal-content .button-wrap .btn.confirm span {
    font-size: 4.2vw;
    font-weight: 600;
  }
}
#main .modal-container .modal-body .modal-content .button-wrap .btn.confirm span::before {
  content: '';
  display: inline-block;
  background: url(../img/common/icn_bid_detail.svg) no-repeat;
  background-size: 16px auto;
  background-position: center;
  width: 18px;
  height: 18px;
  position: absolute;
  top: calc(50% - 8px);
  left: -30px;
}
@media screen and (max-width: 767px) {
  #main .modal-container .modal-body .modal-content .button-wrap .btn.confirm span::before {
    background-size: 4vw auto;
    width: 4.4vw;
    height: 4.4vw;
    top: calc(50% - 2vw);
    left: -7vw;
  }
}
#main .modal-container .modal-body .modal-content .button-wrap .btn.cancel {
  width: 280px;
  max-width: calc(100% - 2rem);
  height: 56px;
  margin: 0;
  color: #e98181;
  background-color: #fff;
  border-radius: 50px;
  border: 1px solid #e98181;
}
@media screen and (max-width: 767px) {
  #main .modal-container .modal-body .modal-content .button-wrap .btn.cancel {
    -webkit-box-ordinal-group: 2;
    -ms-flex-order: 1;
    order: 1;
    width: 100%;
    height: 14vw;
    max-width: 100%;
    border-radius: 20vw;
  }
}
#main .modal-container .modal-body .modal-content .button-wrap .btn.cancel:hover {
  padding-left: 0;
  background-color: #e98181;
  opacity: 1;
}
#main .modal-container .modal-body .modal-content .button-wrap .btn.cancel:hover span {
  color: #fff;
}
#main .modal-container .modal-body .modal-content .button-wrap .btn.cancel span {
  position: relative;
  display: inline-block;
  color: #e98181;
  font-size: 1rem;
  font-weight: 500;
}
@media screen and (max-width: 767px) {
  #main .modal-container .modal-body .modal-content .button-wrap .btn.cancel span {
    font-size: 4.2vw;
    font-weight: 600;
  }
}

/***********************************************************************
 * *
 * *----------------------------------------------------------------------
 * *商品データ
 * *********************************************************************** */
/* スライド
 * *========================================== */
@media screen and (max-width: 767px) {
  #main
    #item-data
    .item_d-main
    .item_d-main-visual
    .slider_wrap
    ul.slider-nav
    .slick-list
    .slick-track {
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    width: 100% !important;
    -webkit-transform: unset !important;
    transform: unset !important;
  }
}
@media screen and (max-width: 767px) {
  #main
    #item-data
    .item_d-main
    .item_d-main-visual
    .slider_wrap
    ul.slider-nav
    .slick-list
    .slick-track
    li {
    /* width: calc(25% - 6px) !important; */
    height: auto;
    margin: 0 3px 6px;
  }
}
@media screen and (max-width: 767px) {
  #main
    #item-data
    .item_d-main
    .item_d-main-visual
    .slider_wrap
    ul.slider-nav
    .slick-list
    .slick-track
    li.slick-cloned {
    display: none;
  }
}

/* 商品情報・価格 など
 * *========================================== */
/* ---------------------------
 * *情報・価格 など
 * *----------------------------- */
/*///// 商品情報ブロック //// */
#main #item-data a.btn-back-search {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin: 60px auto 0;
  width: 100%;
  max-width: 360px;
  min-height: 60px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: #000;
  color: #fff;
  font-size: 18px;
  font-weight: 700;
  border-radius: 100vh;
  margin-top: 60px;
  padding: 10px 20px;
}
#main #item-data a.btn-back-search img {
  width: 19px;
  position: relative;
  top: -2px;
  margin-left: 15px;
}

#main
  #item-data.nego
  .item_d-main
  .item_d-main-visual
  .slider_wrap
  .slick-slider
  .slick-list
  .slick-track
  figure.slick-active {
  position: relative;
}
#main
  #item-data.nego
  .item_d-main
  .item_d-main-visual
  .slider_wrap
  .slick-slider
  .slick-list
  .slick-track
  figure.slick-active:after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  aspect-ratio: 1;
  padding: 0;
  background-image: url(../img/common/icn_nego.png);
  background-size: 60% auto;
  background-position: center center;
  background-repeat: no-repeat;
  z-index: 10;
}
#main #item-data.nego .item_d-main .item_d-main-txt .place-bid:after {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  content: '';
  width: 100%;
  height: 100%;
  background: rgba(240, 240, 240, 0.5);
  z-index: 2;
}

#main
  #item-data.soldout
  .item_d-main
  .item_d-main-visual
  .slider_wrap
  .slick-slider
  .slick-list
  .slick-track
  figure.slick-active {
  position: relative;
}
#main
  #item-data.soldout
  .item_d-main
  .item_d-main-visual
  .slider_wrap
  .slick-slider
  .slick-list
  .slick-track
  figure.slick-active:after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  aspect-ratio: 1;
  padding: 0;
  background-image: url(../img/common/layer_soldout.png);
  background-size: 60% auto;
  background-position: center center;
  background-repeat: no-repeat;
  z-index: 10;
}
#main #item-data.soldout .item_d-main .item_d-main-txt .place-bid:after {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  content: '';
  width: 100%;
  height: 100%;
  background: rgba(240, 240, 240, 0.5);
  z-index: 2;
}

/*///// 価格情報ブロック //// */
/* 入札状況 */
@media only screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-price .status {
    padding: 15px;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
  }
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-price .status p {
    margin-right: 5px;
  }
}
/* ボタン */
@media only screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-system .btn-box a {
    font-size: 16px;
  }
}
/* 閲覧・お気に入り・入札数 */
@media only screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-system ul.action li + li {
    margin-left: 15%;
  }
}
/* 入札フォーム */
@media only screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-system .bidForm .bidPrice {
    display: block;
  }
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-system .bidForm .bidPrice p.bidP-tit {
    font-size: 16px;
  }
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-system .bidForm .bidPrice .bidP-txt {
    font-size: 34px;
    margin-top: 5px;
  }
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .item_d-main-system
    .bidForm
    .bidPrice
    .bidP-txt
    span.yen {
    font-size: 26px;
  }
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .item_d-main-system
    .bidForm
    .bidPrice
    .bidP-txt
    input.ipt-price {
    font-size: 34px;
    width: 130px;
    height: 50px;
  }
}
/*# sourceMappingURL=detail.css.map */
