const Define = require(`${process.env.COMMON_LAYER_PATH}define`)
const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`)
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`)
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`)
const xlsx = require(`${process.env.COMMON_LAYER_PATH}xlsx-template.js`)
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`)
const pool = new PgPool(process.env.READ_ONLY_PGHOST)

const getConstants = ({keyStrings, tenantNo, languageCode}) => {
  // Get constant
  console.log('GET CONSTANT')
  if (!keyStrings || keyStrings.length === 0) {
    return Promise.resolve([])
  }
  const sqlParams = [keyStrings, tenantNo, languageCode]
  return pool
    .rlsQuery(tenantNo, Define.QUERY.GET_CONSTANTS_BY_KEYS, sqlParams)
    .then(data => {
      return Promise.resolve(data)
    })
}

exports.handle = (e, ctx, cb) => {
  console.log('e', e)
  const params = Base.parseRequestBody(e.body)
  console.log(`params = ${JSON.stringify(params)}`)
  console.log('export-items-xlsx-file')

  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        Base.extractTenantId(e)
      )
    )
    .then(() => {
      const rules = require('./validation-rules.js')
      // Get language code list
      return pool
        .rlsQuery(
          Base.extractTenantId(e),
          Define.QUERY.GET_TENANT_LANGUAGE_LIST_FUNCTION,
          [Base.extractTenantId(e)]
        )
        .then(data => {
          const languages = data
          console.log('Tenant language list data: ', languages)
          const languageList =
            languages && languages.length > 0
              ? languages[0].language_code_list
              : []
          rules.language_code.CONTAIN_CHECK_LIST = languageList || []
          return Promise.resolve()
        })
        .then(() => {
          console.log('VALIDATION')
          // Validate parameters
          return Validator.validation(params, rules)
        })
    })
    .then(() => {
      console.log('GET ITEMS XLSX')
      const sqlParams = [
        Base.extractTenantId(e),
        params.exhibition_no,
        params.language_code,
      ]
      const sql = Define.QUERY.GET_ITEMS_XLSX_FUNCTION
      console.log(`sql: ${sql}`)
      return pool.rlsQuery(Base.extractTenantId(e), sql, sqlParams)
    })
    .then(exhs => {
      console.log('FILL IN DATA')
      const exh = exhs && exhs.length > 0 ? exhs[0] : []
      if (!exh || !exh.items || exh.items.length === 0) {
        return Promise.reject({
          status: 400,
          errors: {
            exhibition_no: Define.MESSAGE.E020005,
          },
        })
      }
      // Get constants
      return Promise.all([
        getConstants({
          keyStrings: exh.constant_key_strings,
          tenantNo: Base.extractTenantId(e),
          languageCode: null,
        }),
        pool.rlsQuery(
          Base.extractTenantId(e),
          Define.QUERY.GET_CATEGORY_TREE_FUNCTION,
          [Base.extractTenantId(e)]
        ),
        pool.rlsQuery(
          Base.extractTenantId(e),
          Define.QUERY.GET_CATEGORY_NODE_FUNCTION,
          [Base.extractTenantId(e)]
        ),
      ]).then(([constants, categoryTrees, categoryNodes]) => {
        // Common headers for Excel
        const excelHeaders = {
          ...Define.XLSX.EXPORT.EXHIBITION.COLUMN_NAME,
        }
        const headerTypes = {
          ...Define.XLSX.EXPORT.EXHIBITION.TYPE,
        }
        // Localized headers
        const items = exh.items || []
        items.map(row => {
          // Get localized data
          const localized =
            row.localized_json_array?.find(
              x => x.language_code === params.language_code
            )?.field_map || []
          localized.map(x => {
            excelHeaders[x.physical_name] = x.logical_name
            headerTypes[x.physical_name] = xlsx.getXlsxType(x.input_type)
          })
        })
        // Prepare excel settings
        const excelSettings = {
          COLUMN_NAME: excelHeaders,
          TYPE: headerTypes,
          KEY_FORMAT: Define.XLSX.EXPORT.EXHIBITION.KEY_FORMAT.replace(
            '{3}',
            params.language_code
          ),
        }
        console.log('excelSettings', excelSettings)

        // Items data for excel
        const convertedItems = items.map(row => {
          // Get localized data
          const localized =
            row.localized_json_array?.find(
              x => x.language_code === params.language_code
            )?.field_map || []
          localized.map(x => {
            // Data base on field_map type
            switch (x.input_type) {
              case 'text':
                row[x.physical_name] = Base.nullToString(x.value)
                break
              case 'file':
                row[x.physical_name] = Base.nullToString(x.value)
                break
              case 'checkbox':
                row[x.physical_name] =
                  (constants || []).find(
                    c =>
                      c.key_string === x.input_data_list?.key_string &&
                      c.language_code === params.language_code &&
                      c.value1 === x.value
                  )?.value2 || ''
                break
              case 'pulldown':
                row[x.physical_name] =
                  (constants || []).find(
                    c =>
                      c.key_string === x.input_data_list?.key_string &&
                      c.language_code === params.language_code &&
                      c.value1 === x.value
                  )?.value2 || ''
                break
              default:
                row[x.physical_name] = Base.nullToString(x.value)
                break
            }
          })
          console.log('localized', localized)

          // 最低入札数量
          row.lowest_bid_quantity = row.lowest_bid_quantity || 0
          row.lowest_bid_price = row.lowest_bid_price || 0
          row.deal_bid_price = row.deal_bid_price || 0
          // 最低落札数量
          row.lowest_bid_accept_quantity = row.lowest_bid_accept_quantity || 0
          row.lowest_bid_accept_price = row.lowest_bid_accept_price || 0
          // Recommend flag to text
          if (row.recommend_flag === 1) {
            row.recommend_flag = 'あり'
          } else {
            row.recommend_flag = 'なし'
          }
          // Category
          row.category_tree_name = row.category_tree_id
            ? categoryTrees.find(
                ct => ct.category_tree_id === row.category_tree_id
              )?.category_tree_name || ''
            : ''
          row.category_node_name = row.category_node_id
            ? categoryNodes.find(
                cn => cn.category_node_id === row.category_node_id
              )?.category_node_name || ''
            : ''

          return {
            ...row,
          }
        })
        return Promise.resolve({excelSettings, convertedItems})
      })
    })
    .then(({excelSettings, convertedItems}) => {
      return Base.uploadXlsxToS3(convertedItems, excelSettings)
    })
    .then(result => {
      const data = {
        url: result,
      }
      return Base.createSuccessResponse(cb, data)
    })
    .catch(error => Base.createErrorResponse(cb, error))
}
