// StyleIDはstyleSheet.xmlに設定されている
const XLSX_STYLEID = {
  GENERAL: 3, // 標準
  NUMBER: 1, // 数値
  CURRENCY: 2, // 通貨
  ACCOUNTING: 4, // 会計
  DATE_SHORT: 5, // 短い日付
  DATE_LONG: 6, // 長い日付
  TIME: 7, // 時刻
  PERCENT: 8, // PERCENT
  FRACTION: 9, // 分数
  SCIENTIFIC: 10, // 指数
  TEXT: 0, // 文字列
  WRAP_TEXT: 22, // 文字列を折り返す
  CURRENCY_WITHOUT_UNIT: 23, // 単位付けない通貨

  DISABLED_GENERAL: 14, // 標準
  DISABLED_NUMBER: 12, // 数値
  DISABLED_CURRENCY: 13, // 通貨
  DISABLED_ACCOUNTING: 15, // 会計
  DISABLED_DATE_SHORT: 16, // 短い日付
  DISABLED_DATE_LONG: 17, // 長い日付
  DISABLED_TIME: 18, // 時刻
  DISABLED_PERCENT: 19, // PERCENT
  DISABLED_FRACTION: 20, // 分数
  DISABLED_SCIENTIFIC: 21, // 指数
  DISABLED_TEXT: 11, // 文字列
}

const XLSX_LIST = {
  EXPORT: {
    // 会員一覧
    MEMBER: {
      COLUMN_NAME: {
        memberId: '会員ID',
        memberRequestNo: '会員申請番号',
        statusName: 'ステータス',
        bidAllowFlag: 'WEB応札',
        emailDeliveryFlag: 'メール配信',
        memo: '備考',
      },
      TYPE: {},
      KEY_FORMAT: 'csv-download/{0}-{1}/会員情報.xlsx',
    },
    MEMBER_XLSX: {
      // 項目の順番必要
      COLUMN_NAME: {
        member_id: '会員ID',
        nickname: 'ニックネーム',
        memberName: '会員名',
        memberNameKana: '会員名（カナ）',
        email: 'メールアドレス',
        sexName: '性別',
        birthdayChangeFormat: '生年月日',
        homePost: '郵便番号(自宅)',
        homePrefecture: '都道府県(自宅)',
        homeAddress1: '市区町村(自宅)',
        homeAddress2: 'その他住所等(自宅)',
        homePhone: '電話番号',
        bid_allow_flag: '入札',
        email_delivery_flag: 'メール配信',
        memo: '備考',
        statusName: '会員の区分',
        create_datetime: '登録日時',
        update_datetime: '更新日時',
      },
      TYPE: {
        member_id: XLSX_STYLEID.DISABLED_TEXT,
        memo: XLSX_STYLEID.WRAP_TEXT,
      },
      KEY_FORMAT: 'csv-download/{0}-{1}/会員情報.xlsx',
    },
    // 商品についてお問い合わせ
    INQUIRY_1: {
      COLUMN_NAME: {
        inquiry_no: 'お問い合わせID',
        member_id: '会員ID',
        customer_code: '取引先ID',
        companyName: '会社名',
        memberName: '氏名・担当者',
        email: 'メールアドレス',
        contents: 'お問い合わせ内容',
        create_datetime: 'お問い合わせ日時',
        manageNo: '商品ID',
        productName: '商品名',
        maker: 'メーカー',
        capacity: '容量',
        color: '色',
        rank: 'グレード',
        sim: 'SIM',
        note1: '備考1',
        note2: '備考2',
      },
      KEY_FORMAT: 'csv-download/{0}/お問い合わせ/商品についてお問い合わせ.xlsx',
    },
    // 普通のお問合せ
    INQUIRY_9: {
      COLUMN_NAME: {
        inquiry_no: 'お問い合わせID',
        member_id: '会員ID',
        customer_code: '取引先ID',
        companyName: '会社名',
        memberName: '氏名・担当者',
        email: 'メールアドレス',
        contents: 'お問い合わせ内容',
        create_datetime: 'お問い合わせ日時',
      },
      KEY_FORMAT: 'csv-download/{0}/お問い合わせ/お問い合わせ.xlsx',
    },
    // 出展機
    LOT: {
      COLUMN_NAME: {
        item_no: '商品番号',
        lot_id: 'ロットNo',
        recommend_flag: 'おすすめ',
        manage_no: '台帳番号',
        area: '在庫場所',
        category: 'カテゴリー',
        maker: 'メーカー',
        model: 'モデル',
        unit: '号機',
        modelYear: '年式',
        loadQuantity: '最大定格総荷重',
        amtArea: '営業仕切(円)',
        zikPrice: '実質在庫価格(円)',
        lowest_bid_price: '最低入札価格(円)',
        lowest_bid_accept_price: '最低落札価格(円)',
      },
      TYPE: {
        item_no: XLSX_STYLEID.DISABLED_TEXT,
        lot_id: XLSX_STYLEID.TEXT,
        recommend_flag: XLSX_STYLEID.TEXT,
        manage_no: XLSX_STYLEID.DISABLED_TEXT,
        area: XLSX_STYLEID.DISABLED_TEXT,
        category: XLSX_STYLEID.DISABLED_TEXT,
        maker: XLSX_STYLEID.DISABLED_TEXT,
        model: XLSX_STYLEID.DISABLED_TEXT,
        unit: XLSX_STYLEID.DISABLED_TEXT,
        modelYear: XLSX_STYLEID.DISABLED_TEXT,
        loadQuantity: XLSX_STYLEID.DISABLED_GENERAL,
        amtArea: XLSX_STYLEID.DISABLED_CURRENCY,
        zikPrice: XLSX_STYLEID.DISABLED_CURRENCY,
        lowest_bid_price: XLSX_STYLEID.CURRENCY,
        lowest_bid_accept_price: XLSX_STYLEID.CURRENCY,
      },
      KEY_FORMAT: 'csv-download/{0}-{1}/出展.xlsx',
    },
    // 出品一覧
    EXHIBITION: {
      COLUMN_NAME: {
        manage_no: '商品ID',
        recommend_flag: 'おすすめ',
        lowest_bid_price: '最低入札単価',
        lowest_bid_accept_price: '最低落札単価',
        deal_bid_price: '即決価格',
        category_tree_name: 'カテゴリー親',
        category_node_name: 'カテゴリー子',
      },
      TYPE: {
        manage_no: XLSX_STYLEID.DISABLED_TEXT,
        category: XLSX_STYLEID.TEXT,
        lowest_bid_price: XLSX_STYLEID.CURRENCY_WITHOUT_UNIT,
        lowest_bid_accept_price: XLSX_STYLEID.CURRENCY_WITHOUT_UNIT,
        sku_id: XLSX_STYLEID.TEXT,
      },
      KEY_FORMAT: 'csv-download/{1}-{2}/出展_{3}.xlsx',
    },
  },
  IMPORT: {
    // 会員
    MEMBER: {
      COLUMN_NAME: {
        memberId: '会員ID',
        memberRequestNo: '会員申請番号',
        statusName: 'ステータス',
        bidAllowFlag: 'WEB応札',
        emailDeliveryFlag: 'メール配信',
        memo: '備考',
      },
    },
    // 出展機
    LOT: {
      COLUMN_NAME: {
        商品番号: 'item_no',
        ロットNo: 'lot_id',
        おすすめ: 'recommend_flag',
        台帳番号: 'manage_no',
        在庫場所: 'area',
        カテゴリー: 'category',
        メーカー: 'maker',
        モデル: 'model',
        号機: 'unit',
        年式: 'modelYear',
        最大定格総荷重: 'loadQuantity',
        '営業仕切(円)': 'amtArea',
        '実質在庫価格(円)': 'zikPrice',
        '最低入札価格(円)': 'lowest_bid_price',
        '最低落札価格(円)': 'lowest_bid_accept_price',
      },
    },
    // 出品
    EXHIBITION: {
      COLUMN_NAME: {
        manage_no: '商品ID',
        recommend_flag: 'おすすめ',
        lowest_bid_price: '最低入札単価',
        lowest_bid_accept_price: '最低落札単価',
        deal_bid_price: '即決価格',
        category_tree_name: 'カテゴリー親',
        category_node_name: 'カテゴリー子',
      },
    },
  },
}

module.exports = XLSX_LIST
